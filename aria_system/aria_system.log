2025-06-23 23:54:30,916 - __main__ - INFO - Initializing database...
2025-06-23 23:54:30,963 - aria_system.database.supabase_client - INFO - Schema file found. Please execute the schema manually in Supabase SQL editor.
2025-06-23 23:54:30,964 - aria_system.database.supabase_client - INFO - Schema file location: /Users/<USER>/Development/zurich-UC05-claims-liability/zurich-UC05/aria_system/database/supabase_schema.sql
2025-06-23 23:54:30,964 - __main__ - INFO - Database initialized successfully
2025-06-24 00:24:29,208 - __main__ - INFO - Starting ARIA API server on 0.0.0.0:8000
2025-06-24 00:24:29,317 - main - INFO - Starting ARIA Claims Processing System...
2025-06-24 00:24:29,450 - aria_system.services.notification_service - INFO - HumanLayer notification channels initialized: Email, Slack
2025-06-24 00:24:29,503 - aria_system.services.notification_service - INFO - HumanLayer notification channels initialized: Email, Slack
2025-06-24 00:24:29,590 - main - INFO - <PERSON> system started successfully
2025-06-24 00:24:29,590 - aria_system.core.workflow_orchestrator - INFO - Starting ARIA Workflow Orchestrator...
2025-06-24 00:24:29,591 - aria_system.services.email_monitor - INFO - Starting email monitoring service...
2025-06-24 00:24:31,633 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 00:24:31,634 - aria_system.services.slack_service - ERROR - Failed to start Slack bot: name 'AsyncSocketModeHandler' is not defined
2025-06-24 00:24:31,634 - main - INFO - Shutting down ARIA system...
2025-06-24 00:24:31,634 - aria_system.core.workflow_orchestrator - INFO - Stopping ARIA Workflow Orchestrator...
2025-06-24 00:24:31,634 - aria_system.services.email_monitor - INFO - Stopping email monitoring service...
2025-06-24 00:24:31,634 - main - INFO - ARIA system shutdown complete
2025-06-24 00:24:48,102 - __main__ - INFO - Starting ARIA API server on 0.0.0.0:8001
2025-06-24 00:24:48,124 - main - INFO - Starting ARIA Claims Processing System...
2025-06-24 00:24:48,243 - aria_system.services.notification_service - INFO - HumanLayer notification channels initialized: Email, Slack
2025-06-24 00:24:48,293 - aria_system.services.notification_service - INFO - HumanLayer notification channels initialized: Email, Slack
2025-06-24 00:24:48,380 - main - INFO - ARIA system started successfully
2025-06-24 00:24:48,380 - aria_system.core.workflow_orchestrator - INFO - Starting ARIA Workflow Orchestrator...
2025-06-24 00:24:48,382 - aria_system.services.email_monitor - INFO - Starting email monitoring service...
2025-06-24 00:24:50,370 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 00:24:50,371 - aria_system.services.slack_service - ERROR - Failed to start Slack bot: name 'AsyncSocketModeHandler' is not defined
2025-06-24 00:25:15,734 - httpx - INFO - HTTP Request: GET https://tlduggpohclrgxbvuzhd.supabase.co/rest/v1/claims?select=id&limit=1 "HTTP/2 404 Not Found"
2025-06-24 00:25:15,737 - aria_system.database.supabase_client - ERROR - Database health check failed: {'message': 'relation "public.claims" does not exist', 'code': '42P01', 'hint': None, 'details': None}
2025-06-24 00:25:22,444 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 00:25:38,161 - __main__ - INFO - Starting ARIA dashboard on localhost:8501
2025-06-24 00:25:54,063 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 00:26:26,255 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 00:26:57,755 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 00:27:29,192 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 00:28:01,448 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 00:28:32,588 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 00:29:04,834 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 00:29:36,270 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 00:30:08,424 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 00:30:40,495 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 00:30:42,372 - httpx - INFO - HTTP Request: GET https://tlduggpohclrgxbvuzhd.supabase.co/rest/v1/claims?select=id&limit=1 "HTTP/2 200 OK"
2025-06-24 00:31:12,465 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 00:31:44,833 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 00:32:03,940 - httpx - INFO - HTTP Request: GET https://tlduggpohclrgxbvuzhd.supabase.co/rest/v1/claims?select=id&limit=1 "HTTP/2 200 OK"
2025-06-24 00:32:16,883 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 00:32:24,755 - aria_system.core.workflow_orchestrator - ERROR - Error processing new claim: 'sender_email'
2025-06-24 00:32:48,327 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 00:33:20,475 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 00:33:52,819 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 00:34:24,166 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 00:34:55,704 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 00:35:14,191 - aria_system.core.workflow_orchestrator - ERROR - Error processing new claim: 'sender_email'
2025-06-24 00:35:27,791 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 00:35:59,823 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 00:36:25,559 - __main__ - INFO - Starting ARIA API server on 0.0.0.0:8001
2025-06-24 00:36:25,580 - main - INFO - Starting ARIA Claims Processing System...
2025-06-24 00:36:25,703 - aria_system.services.notification_service - INFO - HumanLayer notification channels initialized: Email, Slack
2025-06-24 00:36:25,752 - aria_system.services.notification_service - INFO - HumanLayer notification channels initialized: Email, Slack
2025-06-24 00:36:25,838 - main - INFO - ARIA system started successfully
2025-06-24 00:36:25,838 - aria_system.core.workflow_orchestrator - INFO - Starting ARIA Workflow Orchestrator...
2025-06-24 00:36:25,839 - aria_system.services.email_monitor - INFO - Starting email monitoring service...
2025-06-24 00:36:27,516 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 00:36:27,518 - aria_system.services.slack_service - ERROR - Failed to start Slack bot: name 'AsyncSocketModeHandler' is not defined
2025-06-24 00:36:48,285 - aria_system.core.workflow_orchestrator - ERROR - Error processing new claim: 'sender_email'
2025-06-24 00:36:58,796 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 00:37:29,968 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 00:38:01,251 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 00:38:29,651 - aria_system.core.workflow_orchestrator - ERROR - Error processing new claim: 'sender_email'
2025-06-24 00:38:32,435 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 00:39:03,612 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 00:39:13,415 - aria_system.core.workflow_orchestrator - ERROR - Error processing new claim: 'sender_email'
2025-06-24 00:39:34,824 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 00:40:06,178 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 00:40:37,307 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 00:41:08,334 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 00:41:45,327 - __main__ - INFO - Starting ARIA API server on 0.0.0.0:8001
2025-06-24 00:41:45,347 - main - INFO - Starting ARIA Claims Processing System...
2025-06-24 00:41:45,473 - aria_system.services.notification_service - INFO - HumanLayer notification channels initialized: Email, Slack
2025-06-24 00:41:45,524 - aria_system.services.notification_service - INFO - HumanLayer notification channels initialized: Email, Slack
2025-06-24 00:41:45,612 - main - INFO - ARIA system started successfully
2025-06-24 00:41:45,613 - aria_system.core.workflow_orchestrator - INFO - Starting ARIA Workflow Orchestrator...
2025-06-24 00:41:45,613 - aria_system.services.email_monitor - INFO - Starting email monitoring service...
2025-06-24 00:41:46,631 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 00:41:46,632 - aria_system.services.slack_service - ERROR - Failed to start Slack bot: name 'AsyncSocketModeHandler' is not defined
2025-06-24 00:42:15,517 - aria_system.core.workflow_orchestrator - ERROR - Error processing new claim: 'sender_email'
2025-06-24 00:42:17,658 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 00:42:48,788 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 00:42:54,526 - httpx - INFO - HTTP Request: GET https://tlduggpohclrgxbvuzhd.supabase.co/rest/v1/claims?select=id&limit=1 "HTTP/2 200 OK"
2025-06-24 00:43:19,813 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 00:43:51,005 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 00:44:22,279 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 00:44:53,509 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 00:45:24,834 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 00:45:56,076 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 10:42:48,535 - __main__ - INFO - Starting ARIA API server on 0.0.0.0:8000
2025-06-24 10:42:48,617 - main - INFO - Starting ARIA Claims Processing System...
2025-06-24 10:42:48,744 - aria_system.services.notification_service - INFO - HumanLayer notification channels initialized: Email, Slack
2025-06-24 10:42:48,796 - aria_system.services.notification_service - INFO - HumanLayer notification channels initialized: Email, Slack
2025-06-24 10:42:48,885 - main - INFO - ARIA system started successfully
2025-06-24 10:42:48,885 - aria_system.core.workflow_orchestrator - INFO - Starting ARIA Workflow Orchestrator...
2025-06-24 10:42:48,886 - aria_system.services.email_monitor - INFO - Starting email monitoring service...
2025-06-24 10:42:50,389 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 10:42:50,390 - aria_system.services.slack_service - ERROR - Failed to start Slack bot: name 'AsyncSocketModeHandler' is not defined
2025-06-24 10:42:51,769 - httpx - INFO - HTTP Request: GET https://tlduggpohclrgxbvuzhd.supabase.co/rest/v1/claims?select=%2A&updated_at=lt.2025-06-24T08%3A42%3A50.390985&status=in.%28received%2Cdocuments_processing%2Cai_analysis%29 "HTTP/2 200 OK"
2025-06-24 10:43:21,526 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 10:43:43,006 - __main__ - INFO - Starting ARIA API server on 0.0.0.0:8001
2025-06-24 10:43:43,025 - main - INFO - Starting ARIA Claims Processing System...
2025-06-24 10:43:43,147 - aria_system.services.notification_service - INFO - HumanLayer notification channels initialized: Email, Slack
2025-06-24 10:43:43,198 - aria_system.services.notification_service - INFO - HumanLayer notification channels initialized: Email, Slack
2025-06-24 10:43:43,286 - main - INFO - ARIA system started successfully
2025-06-24 10:43:43,286 - aria_system.core.workflow_orchestrator - INFO - Starting ARIA Workflow Orchestrator...
2025-06-24 10:43:43,286 - aria_system.services.email_monitor - INFO - Starting email monitoring service...
2025-06-24 10:43:44,462 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 10:43:44,462 - aria_system.services.slack_service - ERROR - Failed to start Slack bot: name 'AsyncSocketModeHandler' is not defined
2025-06-24 10:43:45,487 - httpx - INFO - HTTP Request: GET https://tlduggpohclrgxbvuzhd.supabase.co/rest/v1/claims?select=%2A&updated_at=lt.2025-06-24T08%3A43%3A44.462940&status=in.%28received%2Cdocuments_processing%2Cai_analysis%29 "HTTP/2 200 OK"
2025-06-24 10:44:15,592 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 10:44:19,072 - httpx - INFO - HTTP Request: GET https://tlduggpohclrgxbvuzhd.supabase.co/rest/v1/claims?select=id&limit=1 "HTTP/2 200 OK"
2025-06-24 10:44:27,650 - httpx - INFO - HTTP Request: GET https://tlduggpohclrgxbvuzhd.supabase.co/rest/v1/claims?select=id&limit=1 "HTTP/2 200 OK"
2025-06-24 10:44:46,823 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 10:45:17,952 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 10:45:45,849 - __main__ - INFO - Starting ARIA API server on 0.0.0.0:8001
2025-06-24 10:45:45,869 - main - INFO - Starting ARIA Claims Processing System...
2025-06-24 10:45:45,994 - aria_system.services.notification_service - INFO - HumanLayer notification channels initialized: Email, Slack
2025-06-24 10:45:46,052 - aria_system.services.notification_service - INFO - HumanLayer notification channels initialized: Email, Slack
2025-06-24 10:45:46,141 - main - INFO - ARIA system started successfully
2025-06-24 10:45:46,141 - aria_system.core.workflow_orchestrator - INFO - Starting ARIA Workflow Orchestrator...
2025-06-24 10:45:46,141 - aria_system.services.email_monitor - INFO - Starting email monitoring service...
2025-06-24 10:45:47,340 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 10:45:47,341 - aria_system.services.slack_service - ERROR - Failed to start Slack bot: name 'AsyncSocketModeHandler' is not defined
2025-06-24 10:45:48,850 - httpx - INFO - HTTP Request: GET https://tlduggpohclrgxbvuzhd.supabase.co/rest/v1/claims?select=%2A&updated_at=lt.2025-06-24T08%3A45%3A47.341735&status=in.%28received%2Cdocuments_processing%2Cai_analysis%29 "HTTP/2 200 OK"
2025-06-24 10:46:12,945 - httpx - INFO - HTTP Request: GET https://tlduggpohclrgxbvuzhd.supabase.co/rest/v1/claims?select=id&limit=1 "HTTP/2 200 OK"
2025-06-24 10:46:18,266 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 10:46:18,885 - httpx - INFO - HTTP Request: GET https://tlduggpohclrgxbvuzhd.supabase.co/rest/v1/agents?select=%2A "HTTP/2 200 OK"
2025-06-24 10:46:24,697 - httpx - INFO - HTTP Request: GET https://tlduggpohclrgxbvuzhd.supabase.co/rest/v1/claims?select=id&limit=1 "HTTP/2 200 OK"
2025-06-24 10:46:25,065 - httpx - INFO - HTTP Request: GET https://tlduggpohclrgxbvuzhd.supabase.co/rest/v1/claims?select=%2A&order=created_at.desc "HTTP/2 200 OK"
2025-06-24 10:46:26,065 - httpx - INFO - HTTP Request: GET https://tlduggpohclrgxbvuzhd.supabase.co/rest/v1/agents?select=%2A "HTTP/2 200 OK"
2025-06-24 10:46:26,530 - httpx - INFO - HTTP Request: GET https://tlduggpohclrgxbvuzhd.supabase.co/rest/v1/agents?select=%2A "HTTP/2 200 OK"
2025-06-24 10:46:26,536 - aria_system.core.workflow_orchestrator - ERROR - Error processing new claim: 'sender_email'
2025-06-24 10:46:26,990 - httpx - INFO - HTTP Request: GET https://tlduggpohclrgxbvuzhd.supabase.co/rest/v1/claims?select=%2A&id=eq.None "HTTP/2 400 Bad Request"
2025-06-24 10:46:26,992 - aria_system.database.supabase_client - ERROR - Error getting claim None: {'message': 'invalid input syntax for type uuid: "None"', 'code': '22P02', 'hint': None, 'details': None}
2025-06-24 10:46:26,992 - main - ERROR - Error getting claim: 
2025-06-24 10:46:42,157 - aria_system.core.workflow_orchestrator - ERROR - Error processing new claim: 'sender_email'
2025-06-24 10:46:51,135 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 10:47:22,264 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 10:47:37,309 - httpx - INFO - HTTP Request: GET https://tlduggpohclrgxbvuzhd.supabase.co/rest/v1/claims?select=%2A&order=created_at.desc "HTTP/2 200 OK"
2025-06-24 10:47:46,055 - __main__ - INFO - Starting ARIA dashboard on localhost:8501
2025-06-24 10:47:53,487 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 10:48:24,832 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 10:48:56,164 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 10:49:27,381 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 10:49:58,603 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 10:50:29,652 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 10:50:49,625 - httpx - INFO - HTTP Request: GET https://tlduggpohclrgxbvuzhd.supabase.co/rest/v1/claims?select=%2A&updated_at=lt.2025-06-24T08%3A50%3A48.852801&status=in.%28received%2Cdocuments_processing%2Cai_analysis%29 "HTTP/2 200 OK"
2025-06-24 10:51:00,887 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 10:51:31,911 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 10:52:03,006 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 10:52:34,374 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 10:53:05,665 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 10:53:36,755 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 10:54:07,911 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 10:54:38,985 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 10:55:10,227 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 10:55:48,409 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 10:55:50,447 - httpx - INFO - HTTP Request: GET https://tlduggpohclrgxbvuzhd.supabase.co/rest/v1/claims?select=%2A&updated_at=lt.2025-06-24T08%3A55%3A49.625928&status=in.%28received%2Cdocuments_processing%2Cai_analysis%29 "HTTP/2 200 OK"
2025-06-24 10:56:20,126 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 10:56:53,237 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 10:57:25,187 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 10:58:03,167 - __main__ - INFO - Starting ARIA API server on 0.0.0.0:8001
2025-06-24 10:58:03,188 - main - INFO - Starting ARIA Claims Processing System...
2025-06-24 10:58:03,316 - aria_system.services.notification_service - INFO - HumanLayer notification channels initialized: Email, Slack
2025-06-24 10:58:03,366 - aria_system.services.notification_service - INFO - HumanLayer notification channels initialized: Email, Slack
2025-06-24 10:58:03,455 - main - INFO - ARIA system started successfully
2025-06-24 10:58:03,455 - aria_system.core.workflow_orchestrator - INFO - Starting ARIA Workflow Orchestrator...
2025-06-24 10:58:03,455 - aria_system.services.email_monitor - INFO - Starting email monitoring service...
2025-06-24 10:58:04,559 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 10:58:04,560 - aria_system.services.slack_service - ERROR - Failed to start Slack bot: name 'AsyncSocketModeHandler' is not defined
2025-06-24 10:58:05,957 - httpx - INFO - HTTP Request: GET https://tlduggpohclrgxbvuzhd.supabase.co/rest/v1/claims?select=%2A&updated_at=lt.2025-06-24T08%3A58%3A04.560552&status=in.%28received%2Cdocuments_processing%2Cai_analysis%29 "HTTP/2 200 OK"
2025-06-24 10:58:12,796 - httpx - INFO - HTTP Request: GET https://tlduggpohclrgxbvuzhd.supabase.co/rest/v1/claims?select=id&limit=1 "HTTP/2 200 OK"
2025-06-24 10:58:21,472 - aria_system.core.workflow_orchestrator - INFO - Processing new <NAME_EMAIL>
2025-06-24 10:58:21,817 - httpx - INFO - HTTP Request: GET https://tlduggpohclrgxbvuzhd.supabase.co/rest/v1/claims?select=id "HTTP/2 200 OK"
2025-06-24 10:58:21,836 - aria_system.database.supabase_client - ERROR - Error creating claim: Object of type datetime is not JSON serializable
2025-06-24 10:58:21,837 - aria_system.core.workflow_orchestrator - ERROR - Error processing new claim: Object of type datetime is not JSON serializable
2025-06-24 10:58:21,837 - aria_system.core.workflow_orchestrator - ERROR - Workflow <NAME_EMAIL>: Object of type datetime is not JSON serializable
2025-06-24 10:58:35,822 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 10:58:50,894 - httpx - INFO - HTTP Request: GET https://tlduggpohclrgxbvuzhd.supabase.co/rest/v1/claims?select=%2A&order=created_at.desc "HTTP/2 200 OK"
2025-06-24 10:59:07,010 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 10:59:38,099 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 10:59:55,556 - __main__ - INFO - Starting ARIA API server on 0.0.0.0:8001
2025-06-24 10:59:55,578 - main - INFO - Starting ARIA Claims Processing System...
2025-06-24 10:59:55,711 - aria_system.services.notification_service - INFO - HumanLayer notification channels initialized: Email, Slack
2025-06-24 10:59:55,764 - aria_system.services.notification_service - INFO - HumanLayer notification channels initialized: Email, Slack
2025-06-24 10:59:55,854 - main - INFO - ARIA system started successfully
2025-06-24 10:59:55,854 - aria_system.core.workflow_orchestrator - INFO - Starting ARIA Workflow Orchestrator...
2025-06-24 10:59:55,855 - aria_system.services.email_monitor - INFO - Starting email monitoring service...
2025-06-24 10:59:57,243 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 10:59:57,244 - aria_system.services.slack_service - ERROR - Failed to start Slack bot: name 'AsyncSocketModeHandler' is not defined
2025-06-24 10:59:58,162 - httpx - INFO - HTTP Request: GET https://tlduggpohclrgxbvuzhd.supabase.co/rest/v1/claims?select=%2A&updated_at=lt.2025-06-24T08%3A59%3A57.245109&status=in.%28received%2Cdocuments_processing%2Cai_analysis%29 "HTTP/2 200 OK"
2025-06-24 11:00:28,567 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 11:00:55,587 - __main__ - INFO - Starting ARIA API server on 0.0.0.0:8001
2025-06-24 11:00:55,607 - main - INFO - Starting ARIA Claims Processing System...
2025-06-24 11:00:55,745 - aria_system.services.notification_service - INFO - HumanLayer notification channels initialized: Email, Slack
2025-06-24 11:00:55,797 - aria_system.services.notification_service - INFO - HumanLayer notification channels initialized: Email, Slack
2025-06-24 11:00:55,889 - main - INFO - ARIA system started successfully
2025-06-24 11:00:55,889 - aria_system.core.workflow_orchestrator - INFO - Starting ARIA Workflow Orchestrator...
2025-06-24 11:00:55,889 - aria_system.services.email_monitor - INFO - Starting email monitoring service...
2025-06-24 11:00:58,056 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 11:00:58,057 - aria_system.services.slack_service - ERROR - Failed to start Slack bot: name 'AsyncSocketModeHandler' is not defined
2025-06-24 11:00:58,471 - httpx - INFO - HTTP Request: GET https://tlduggpohclrgxbvuzhd.supabase.co/rest/v1/claims?select=%2A&updated_at=lt.2025-06-24T09%3A00%3A58.057587&status=in.%28received%2Cdocuments_processing%2Cai_analysis%29 "HTTP/2 200 OK"
2025-06-24 11:01:09,306 - httpx - INFO - HTTP Request: GET https://tlduggpohclrgxbvuzhd.supabase.co/rest/v1/claims?select=id&limit=1 "HTTP/2 200 OK"
2025-06-24 11:01:29,726 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 11:01:59,017 - aria_system.core.workflow_orchestrator - INFO - Processing new <NAME_EMAIL>
2025-06-24 11:01:59,863 - httpx - INFO - HTTP Request: GET https://tlduggpohclrgxbvuzhd.supabase.co/rest/v1/claims?select=id "HTTP/2 200 OK"
2025-06-24 11:02:00,212 - httpx - INFO - HTTP Request: POST https://tlduggpohclrgxbvuzhd.supabase.co/rest/v1/claims "HTTP/2 201 Created"
2025-06-24 11:02:00,214 - aria_system.database.supabase_client - INFO - Created claim: CLAIM-2025-000001
2025-06-24 11:02:00,496 - httpx - INFO - HTTP Request: GET https://tlduggpohclrgxbvuzhd.supabase.co/rest/v1/claims?select=%2A&id=eq.175b22b4-1ab9-44f6-818f-3acf502d95d4 "HTTP/2 200 OK"
2025-06-24 11:02:01,341 - httpx - INFO - HTTP Request: PATCH https://tlduggpohclrgxbvuzhd.supabase.co/rest/v1/claims?id=eq.175b22b4-1ab9-44f6-818f-3acf502d95d4 "HTTP/2 200 OK"
2025-06-24 11:02:01,341 - aria_system.database.supabase_client - INFO - Updated claim 175b22b4-1ab9-44f6-818f-3acf502d95d4
2025-06-24 11:02:01,645 - httpx - INFO - HTTP Request: POST https://tlduggpohclrgxbvuzhd.supabase.co/rest/v1/claim_timeline "HTTP/2 400 Bad Request"
2025-06-24 11:02:01,647 - aria_system.database.supabase_client - ERROR - Error creating timeline entry: {'message': "Could not find the 'actor_email' column of 'claim_timeline' in the schema cache", 'code': 'PGRST204', 'hint': None, 'details': None}
2025-06-24 11:02:01,647 - aria_system.database.supabase_client - INFO - Tracked workflow step 'claim_created' for claim 175b22b4-1ab9-44f6-818f-3acf502d95d4
2025-06-24 11:02:02,058 - httpx - INFO - HTTP Request: GET https://tlduggpohclrgxbvuzhd.supabase.co/rest/v1/claims?select=%2A&id=eq.175b22b4-1ab9-44f6-818f-3acf502d95d4 "HTTP/2 200 OK"
2025-06-24 11:02:03,490 - aria_system.services.email_monitor - ERROR - Error checking emails: b'[AUTHENTICATIONFAILED] Invalid credentials (Failure)'
2025-06-24 11:02:03,697 - aria_system.services.zendesk_service - ERROR - Error creating Zendesk ticket: Cannot connect to host d3v-rozieai5417.zendesk.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: unable to get local issuer certificate (_ssl.c:1000)')]
2025-06-24 11:02:03,697 - aria_system.core.workflow_orchestrator - ERROR - Error processing new claim: Cannot connect to host d3v-rozieai5417.zendesk.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: unable to get local issuer certificate (_ssl.c:1000)')]
2025-06-24 11:02:03,697 - aria_system.core.workflow_orchestrator - ERROR - Workflow <NAME_EMAIL>: Cannot connect to host d3v-rozieai5417.zendesk.com:443 ssl:True [SSLCertVerificationError: (1, '[SSL: CERTIFICATE_VERIFY_FAILED] certificate verify failed: unable to get local issuer certificate (_ssl.c:1000)')]
2025-06-24 11:02:03,698 - aria_system.services.notification_service - WARNING - Template processing_error not found, using fallback: 'processing_error' not found in search path: '/Users/<USER>/Development/zurich-UC05-claims-liability/zurich-UC05/aria_system/aria_system/templates/notifications'
