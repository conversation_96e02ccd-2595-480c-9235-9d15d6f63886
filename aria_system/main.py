"""
ARIA Claims Processing System - Main Application
Complete real-time claims processing with email monitoring, AI analysis, and human workflows
"""

import asyncio
import logging
import signal
import sys
from datetime import datetime
from pathlib import Path
from typing import Optional

import click
import uvicorn
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
import streamlit as st

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

from aria_system.config import Settings
from aria_system.core.workflow_orchestrator import WorkflowOrchestrator
from aria_system.web.agent_portal import AgentPortal
from aria_system.database.supabase_client import SupabaseClient

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('aria_system.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

logger = logging.getLogger(__name__)

# Global instances
settings = Settings()
orchestrator: Optional[WorkflowOrchestrator] = None
app = FastAPI(
    title="ARIA Claims Processing System",
    description="Real-time insurance claims processing with AI and human workflows",
    version="2.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.app.allowed_origins.split(',') if settings.app.allowed_origins else ["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.on_event("startup")
async def startup_event():
    """Initialize the application"""
    global orchestrator
    
    logger.info("Starting ARIA Claims Processing System...")
    
    try:
        # Initialize workflow orchestrator
        orchestrator = WorkflowOrchestrator(settings)
        
        # Start background services
        asyncio.create_task(orchestrator.start_orchestrator())
        
        logger.info("ARIA system started successfully")
        
    except Exception as e:
        logger.error(f"Failed to start ARIA system: {e}")
        raise


@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown"""
    global orchestrator
    
    logger.info("Shutting down ARIA system...")
    
    if orchestrator:
        orchestrator.stop_orchestrator()
    
    logger.info("ARIA system shutdown complete")


# API Routes
@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "message": "ARIA Claims Processing System",
        "version": "2.0.0",
        "status": "running",
        "services": {
            "email_monitoring": True,
            "ai_analysis": True,
            "slack_integration": True,
            "zendesk_integration": True,
            "notifications": True
        }
    }


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    try:
        # Check database connection
        supabase = SupabaseClient(settings)
        db_status = await supabase.health_check()
        
        return {
            "status": "healthy",
            "timestamp": asyncio.get_event_loop().time(),
            "database": "connected" if db_status else "disconnected",
            "orchestrator": "running" if orchestrator and orchestrator.is_running else "stopped"
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        raise HTTPException(status_code=503, detail="Service unhealthy")


@app.post("/api/claims/submit")
async def submit_claim(claim_data: dict, background_tasks: BackgroundTasks):
    """Submit a new claim via API"""
    try:
        if not orchestrator:
            raise HTTPException(status_code=503, detail="Orchestrator not available")
        
        # Process claim in background
        background_tasks.add_task(orchestrator.process_new_claim, claim_data)
        
        return {
            "message": "Claim submitted successfully",
            "status": "processing"
        }
    except Exception as e:
        logger.error(f"Error submitting claim: {e}")
        raise HTTPException(status_code=500, detail="Failed to submit claim")


@app.get("/api/claims/{claim_id}")
async def get_claim(claim_id: str):
    """Get claim details"""
    try:
        supabase = SupabaseClient(settings)
        claim = await supabase.get_claim(claim_id)
        
        if not claim:
            raise HTTPException(status_code=404, detail="Claim not found")
        
        return claim
    except Exception as e:
        logger.error(f"Error getting claim: {e}")
        raise HTTPException(status_code=500, detail="Failed to get claim")


@app.post("/api/claims/{claim_id}/decision")
async def submit_decision(claim_id: str, decision_data: dict, background_tasks: BackgroundTasks):
    """Submit agent decision on claim"""
    try:
        if not orchestrator:
            raise HTTPException(status_code=503, detail="Orchestrator not available")

        # Process decision in background
        background_tasks.add_task(orchestrator.process_agent_decision, claim_id, decision_data)

        return {
            "message": "Decision submitted successfully",
            "status": "processing"
        }
    except Exception as e:
        logger.error(f"Error submitting decision: {e}")
        raise HTTPException(status_code=500, detail="Failed to submit decision")


@app.get("/api/claims")
async def list_claims():
    """List all claims"""
    try:
        supabase = SupabaseClient(settings)
        claims = await supabase.get_all_claims()
        return claims or []
    except Exception as e:
        logger.error(f"Error listing claims: {e}")
        raise HTTPException(status_code=500, detail="Failed to list claims")


@app.get("/api/agents")
async def list_agents():
    """List all agents"""
    try:
        supabase = SupabaseClient(settings)
        agents = await supabase.get_all_agents()
        return agents or []
    except Exception as e:
        logger.error(f"Error listing agents: {e}")
        raise HTTPException(status_code=500, detail="Failed to list agents")


@app.post("/api/test/email-claim")
async def test_email_claim(email_data: dict, background_tasks: BackgroundTasks):
    """Test endpoint to simulate email claim submission and trigger full workflow"""
    try:
        if not orchestrator:
            raise HTTPException(status_code=503, detail="Orchestrator not available")

        # Create simulated email data
        simulated_email = {
            'sender_email': email_data.get('sender_email', '<EMAIL>'),
            'sender_name': email_data.get('sender_name', 'Test User'),
            'subject': email_data.get('subject', 'Auto accident claim - Need assistance'),
            'body': email_data.get('body', 'I was involved in a car accident and need to file a claim.'),
            'attachments': email_data.get('attachments', []),
            'received_at': datetime.now(),
            'message_id': f'test-{datetime.now().timestamp()}'
        }

        # Process through the full workflow
        background_tasks.add_task(orchestrator.process_new_claim, simulated_email)

        return {
            "message": "Email claim simulation started",
            "status": "processing",
            "email_data": simulated_email
        }
    except Exception as e:
        logger.error(f"Error processing test email claim: {e}")
        raise HTTPException(status_code=500, detail="Failed to process test email claim")


@app.post("/api/test/simple-claim")
async def test_simple_claim(email_data: dict):
    """Test endpoint for simple claim creation without Zendesk/external dependencies"""
    try:
        if not orchestrator:
            raise HTTPException(status_code=503, detail="Orchestrator not available")

        # Create simulated email data
        simulated_email = {
            'sender_email': email_data.get('sender_email', '<EMAIL>'),
            'sender_name': email_data.get('sender_name', 'Test Customer'),
            'subject': email_data.get('subject', 'Auto accident claim - Need assistance'),
            'body': email_data.get('body', 'I was involved in a car accident and need to file a claim.'),
            'attachments': email_data.get('attachments', []),
            'received_at': datetime.now().isoformat(),
            'message_id': f'test-{datetime.now().timestamp()}'
        }

        # Create claim record directly
        claim_data = await orchestrator._create_claim_record(simulated_email)

        # Send acknowledgment (skip Zendesk)
        await orchestrator._send_user_acknowledgment(claim_data['id'], simulated_email)

        return {
            "message": "Simple claim created successfully",
            "status": "created",
            "claim_id": claim_data['id'],
            "claim_number": claim_data['claim_number'],
            "email_data": simulated_email
        }
    except Exception as e:
        logger.error(f"Error creating simple claim: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to create simple claim: {str(e)}")


@app.get("/track/{claim_number}")
async def track_claim(claim_number: str):
    """Claim tracking page"""
    try:
        supabase = SupabaseClient(settings)
        claim = await supabase.get_claim_by_number(claim_number)
        
        if not claim:
            return HTMLResponse("""
            <html>
                <head><title>Claim Not Found</title></head>
                <body>
                    <h1>Claim Not Found</h1>
                    <p>The claim number you entered could not be found.</p>
                </body>
            </html>
            """, status_code=404)
        
        # Generate tracking page
        timeline = await supabase.get_claim_timeline(claim['id'])
        
        html_content = f"""
        <html>
            <head>
                <title>Track Claim {claim_number}</title>
                <style>
                    body {{ font-family: Arial, sans-serif; margin: 40px; }}
                    .status {{ padding: 10px; margin: 10px 0; border-radius: 5px; }}
                    .completed {{ background: #d4edda; border: 1px solid #c3e6cb; }}
                    .current {{ background: #fff3cd; border: 1px solid #ffeaa7; }}
                    .pending {{ background: #f8f9fa; border: 1px solid #dee2e6; }}
                </style>
            </head>
            <body>
                <h1>Claim Tracking: {claim_number}</h1>
                <p><strong>Status:</strong> {claim['status']}</p>
                <p><strong>Submitted:</strong> {claim['created_at']}</p>
                
                <h2>Progress Timeline</h2>
                <div class="timeline">
        """
        
        for event in timeline:
            status_class = "completed" if event['created_at'] else "pending"
            html_content += f"""
                <div class="status {status_class}">
                    <strong>{event['title']}</strong><br>
                    {event['description']}<br>
                    <small>{event['created_at'] or 'Pending'}</small>
                </div>
            """
        
        html_content += """
                </div>
            </body>
        </html>
        """
        
        return HTMLResponse(html_content)
        
    except Exception as e:
        logger.error(f"Error tracking claim: {e}")
        return HTMLResponse("""
        <html>
            <head><title>Error</title></head>
            <body>
                <h1>Error</h1>
                <p>An error occurred while tracking your claim.</p>
            </body>
        </html>
        """, status_code=500)


@app.get("/agent/{claim_id}")
async def agent_review(claim_id: str):
    """Agent review page"""
    review_url = f"{settings.app.base_url}/agent-portal?claim_id={claim_id}"
    
    return HTMLResponse(f"""
    <html>
        <head>
            <title>Agent Review</title>
            <meta http-equiv="refresh" content="0; url={review_url}">
        </head>
        <body>
            <p>Redirecting to agent portal...</p>
            <p><a href="{review_url}">Click here if not redirected automatically</a></p>
        </body>
    </html>
    """)


# Webhook endpoints
@app.post("/webhooks/zendesk")
async def zendesk_webhook(webhook_data: dict, background_tasks: BackgroundTasks):
    """Handle Zendesk webhooks"""
    try:
        # Process webhook in background
        background_tasks.add_task(process_zendesk_webhook, webhook_data)
        return {"status": "received"}
    except Exception as e:
        logger.error(f"Error processing Zendesk webhook: {e}")
        raise HTTPException(status_code=500, detail="Webhook processing failed")


@app.post("/webhooks/slack")
async def slack_webhook(webhook_data: dict, background_tasks: BackgroundTasks):
    """Handle Slack webhooks"""
    try:
        # Process webhook in background
        background_tasks.add_task(process_slack_webhook, webhook_data)
        return {"status": "received"}
    except Exception as e:
        logger.error(f"Error processing Slack webhook: {e}")
        raise HTTPException(status_code=500, detail="Webhook processing failed")


async def process_zendesk_webhook(webhook_data: dict):
    """Process Zendesk webhook data"""
    # Implementation for Zendesk webhook processing
    logger.info(f"Processing Zendesk webhook: {webhook_data}")


async def process_slack_webhook(webhook_data: dict):
    """Process Slack webhook data"""
    # Implementation for Slack webhook processing
    logger.info(f"Processing Slack webhook: {webhook_data}")


# CLI Commands
@click.group()
def cli():
    """ARIA Claims Processing System CLI"""
    pass


@cli.command()
@click.option('--host', default='0.0.0.0', help='Host to bind to')
@click.option('--port', default=8000, help='Port to bind to')
@click.option('--reload', is_flag=True, help='Enable auto-reload')
def serve(host: str, port: int, reload: bool):
    """Start the ARIA API server"""
    logger.info(f"Starting ARIA API server on {host}:{port}")
    
    uvicorn.run(
        "main:app",
        host=host,
        port=port,
        reload=reload,
        log_level="info"
    )


@cli.command()
@click.option('--host', default='localhost', help='Host to bind to')
@click.option('--port', default=8501, help='Port to bind to')
def dashboard(host: str, port: int):
    """Start the ARIA dashboard"""
    logger.info(f"Starting ARIA dashboard on {host}:{port}")
    
    # Set up Streamlit configuration
    import subprocess
    import sys
    
    cmd = [
        sys.executable, "-m", "streamlit", "run",
        "aria_system/web/agent_portal.py",
        "--server.address", host,
        "--server.port", str(port),
        "--server.headless", "true"
    ]
    
    subprocess.run(cmd)


@cli.command()
def test_workflow():
    """Test the complete workflow with sample data"""
    logger.info("Testing ARIA workflow...")
    
    async def run_test():
        test_orchestrator = WorkflowOrchestrator(settings)
        
        # Sample email data
        sample_email = {
            'sender_email': '<EMAIL>',
            'sender_name': 'Test User',
            'subject': 'Auto accident claim',
            'body': 'I was involved in a car accident and need to file a claim.',
            'attachments': [],
            'received_at': asyncio.get_event_loop().time(),
            'message_id': 'test-message-123'
        }
        
        try:
            claim_id = await test_orchestrator.process_new_claim(sample_email)
            logger.info(f"Test claim created: {claim_id}")
            
            # Simulate agent decision
            decision_data = {
                'decision': 'approve',
                'reasoning': 'Test approval',
                'confidence': 4,
                'approved_amount': 5000
            }
            
            await test_orchestrator.process_agent_decision(claim_id, decision_data)
            logger.info("Test workflow completed successfully")
            
        except Exception as e:
            logger.error(f"Test workflow failed: {e}")
            raise
    
    asyncio.run(run_test())


@cli.command()
def init_db():
    """Initialize the database schema"""
    logger.info("Initializing database...")
    
    async def run_init():
        supabase = SupabaseClient(settings)
        await supabase.initialize_schema()
        logger.info("Database initialized successfully")
    
    asyncio.run(run_init())


def signal_handler(signum, frame):
    """Handle shutdown signals"""
    logger.info(f"Received signal {signum}, shutting down...")
    sys.exit(0)


if __name__ == "__main__":
    # Register signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Run CLI
    cli()
