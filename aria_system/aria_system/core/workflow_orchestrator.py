"""
ARIA Workflow Orchestrator
Main orchestrator for the complete claims processing workflow
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from enum import Enum

from ..config import Settings
from ..database.supabase_client import SupabaseClient
from ..services.email_monitor import EmailMonitor
from ..services.zendesk_service import ZendeskService
from ..services.notification_service import NotificationService
from ..services.slack_service import SlackService
from ..services.ai_analysis import AIAnalysisService

logger = logging.getLogger(__name__)


class WorkflowStatus(Enum):
    """Workflow status enumeration"""
    RECEIVED = "received"
    DOCUMENTS_PROCESSING = "documents_processing"
    AI_ANALYSIS = "ai_analysis"
    HUMAN_REVIEW = "human_review"
    PENDING_APPROVAL = "pending_approval"
    APPROVED = "approved"
    DENIED = "denied"
    MORE_INFO_REQUIRED = "more_info_required"
    CLOSED = "closed"
    ESCALATED = "escalated"


class WorkflowOrchestrator:
    """Main workflow orchestrator for claims processing"""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.supabase = SupabaseClient(settings)
        
        # Initialize services
        self.email_monitor = EmailMonitor(settings)
        self.zendesk = ZendeskService(settings)
        self.notifications = NotificationService(settings)
        self.slack = SlackService(settings)
        self.ai_analysis = AIAnalysisService(settings)
        
        # Workflow state
        self.is_running = False
        self.active_workflows = {}
    
    async def start_orchestrator(self):
        """Start the workflow orchestrator"""
        logger.info("Starting ARIA Workflow Orchestrator...")
        self.is_running = True
        
        # Start background services
        tasks = [
            asyncio.create_task(self.email_monitor.start_monitoring()),
            asyncio.create_task(self.slack.start_bot()),
            asyncio.create_task(self._workflow_monitor()),
            asyncio.create_task(self._sla_monitor())
        ]
        
        try:
            await asyncio.gather(*tasks)
        except Exception as e:
            logger.error(f"Error in workflow orchestrator: {e}")
        finally:
            self.is_running = False
    
    def stop_orchestrator(self):
        """Stop the workflow orchestrator"""
        logger.info("Stopping ARIA Workflow Orchestrator...")
        self.is_running = False
        self.email_monitor.stop_monitoring()
    
    async def process_new_claim(self, email_details: Dict) -> str:
        """Process a new claim from email submission"""
        try:
            logger.info(f"Processing new claim from {email_details['sender_email']}")
            
            # Step 1: Create claim record
            claim_data = await self._create_claim_record(email_details)
            claim_id = claim_data['id']
            
            # Step 2: Create Zendesk ticket
            await self._create_zendesk_ticket(claim_id, email_details)
            
            # Step 3: Send acknowledgment to user
            await self._send_user_acknowledgment(claim_id, email_details)
            
            # Step 4: Notify Slack team
            await self._notify_slack_team(claim_id)
            
            # Step 5: Start document processing
            await self._start_document_processing(claim_id)
            
            # Step 6: Update status
            await self._update_claim_status(
                claim_id, 
                WorkflowStatus.DOCUMENTS_PROCESSING,
                "Documents received and processing started"
            )
            
            logger.info(f"Claim {claim_data['claim_number']} created and workflow started")
            return claim_id
            
        except Exception as e:
            logger.error(f"Error processing new claim: {e}")
            await self._handle_workflow_error(email_details, str(e))
            raise
    
    async def process_document_analysis_complete(self, claim_id: str):
        """Handle completion of document analysis"""
        try:
            logger.info(f"Document analysis completed for claim {claim_id}")

            # Update status
            await self._update_claim_status(
                claim_id,
                WorkflowStatus.AI_ANALYSIS,
                "Documents processed, AI analysis in progress"
            )

            # Start AI analysis
            analysis_results = await self.ai_analysis.analyze_claim_documents(claim_id)

            # Store AI analysis results in database
            await self.supabase.store_ai_analysis(claim_id, analysis_results)

            # Track workflow step
            await self.supabase.track_workflow_step(
                claim_id,
                'ai_analysis_completed',
                'completed',
                {
                    'confidence_score': analysis_results.get('overall', {}).get('confidence_score', 0),
                    'claim_type': analysis_results.get('classification', {}).get('claim_type', 'unknown'),
                    'processing_priority': analysis_results.get('overall', {}).get('processing_priority', 3)
                }
            )

            # Log AI analysis results to Zendesk
            await self._log_ai_analysis_to_zendesk(claim_id, analysis_results)

            # ALL CLAIMS REQUIRE HUMAN REVIEW - NO AUTO-APPROVAL
            # Always assign to human agent regardless of AI confidence
            logger.info(f"Claim {claim_id}: Assigning to human agent (auto-approval disabled)")
            await self._assign_to_human_agent(claim_id, analysis_results)

        except Exception as e:
            logger.error(f"Error in document analysis workflow: {e}")
            await self._log_to_zendesk(claim_id, "Document Analysis Error", f"Error in document analysis: {str(e)}")
            await self._escalate_claim(claim_id, f"Document analysis error: {e}")
    
    async def process_agent_assignment(self, claim_id: str, agent_email: str):
        """Handle agent assignment to claim"""
        try:
            logger.info(f"Assigning claim {claim_id} to agent {agent_email}")

            # Update claim assignment
            await self.supabase.assign_claim(claim_id, agent_email)

            # Get claim data for Zendesk operations
            claim_data = await self.supabase.get_claim(claim_id)

            # Add agent to Zendesk ticket and assign
            if claim_data.get('zendesk_ticket_id'):
                # Assign ticket to agent
                await self.zendesk.assign_ticket(
                    claim_data['zendesk_ticket_id'],
                    agent_email,
                    f"Claim {claim_data['claim_number']} has been assigned to you for review."
                )

                # Add agent as CC to email thread
                await self.zendesk.add_cc_to_ticket(
                    claim_data['zendesk_ticket_id'],
                    [agent_email]
                )

                # Log assignment details to Zendesk
                await self._log_to_zendesk(
                    claim_id,
                    "Agent Assignment",
                    f"Claim assigned to: {agent_email}\n"
                    f"Assignment reason: AI analysis completed, human review required\n"
                    f"Agent has been added to email thread and will receive all updates",
                    is_public=False
                )

            # Update status
            await self._update_claim_status(
                claim_id,
                WorkflowStatus.HUMAN_REVIEW,
                f"Claim assigned to expert for review"
            )

            # Track workflow step
            await self.supabase.track_workflow_step(
                claim_id,
                'agent_assigned',
                'completed',
                {
                    'agent_email': agent_email,
                    'assignment_method': 'automatic'
                },
                actor_email=agent_email
            )

            # Send agent notification
            await self._send_agent_assignment_notification(claim_id, agent_email)

            # Send Slack assignment notification
            await self.slack.send_agent_assignment_notification(claim_data, agent_email)

            # Notify user
            await self._notify_user_assignment(claim_id, agent_email)

        except Exception as e:
            logger.error(f"Error in agent assignment: {e}")
            await self._log_to_zendesk(claim_id, "Assignment Error", f"Error assigning agent: {str(e)}")
            raise
    
    async def process_agent_decision(self, claim_id: str, decision_data: Dict):
        """Handle agent decision on claim"""
        try:
            logger.info(f"Processing agent decision for claim {claim_id}: {decision_data['decision']}")

            # Store decision
            await self.supabase.store_agent_decision(claim_id, decision_data)

            # Track workflow step
            await self.supabase.track_workflow_step(
                claim_id,
                'agent_decision_made',
                'completed',
                {
                    'decision': decision_data['decision'],
                    'reasoning': decision_data.get('reasoning', ''),
                    'confidence_level': decision_data.get('confidence_level', 0)
                },
                actor_email=decision_data.get('agent_email')
            )

            # Log detailed decision to Zendesk
            await self._log_agent_decision_to_zendesk(claim_id, decision_data)

            # Process based on decision type
            if decision_data['decision'] == 'approve':
                await self._process_approval(claim_id, decision_data)
            elif decision_data['decision'] == 'deny':
                await self._process_denial(claim_id, decision_data)
            elif decision_data['decision'] == 'request_more_info':
                await self._request_more_information(claim_id, decision_data)
            elif decision_data['decision'] == 'escalate':
                await self._escalate_claim(claim_id, decision_data['reasoning'])

        except Exception as e:
            logger.error(f"Error processing agent decision: {e}")
            await self._log_to_zendesk(claim_id, "Decision Processing Error", f"Error processing agent decision: {str(e)}")
            raise
    
    async def _create_claim_record(self, email_details: Dict) -> Dict:
        """Create initial claim record"""
        claim_data = {
            'user_email': email_details['sender_email'],
            'user_name': email_details['sender_name'],
            'subject': email_details['subject'],
            'description': email_details['body'],
            'status': WorkflowStatus.RECEIVED.value,
            'reported_date': email_details['received_at'],
            'metadata': {
                'source': 'email',
                'message_id': email_details['message_id'],
                'attachment_count': len(email_details['attachments'])
            }
        }
        
        claim = await self.supabase.create_claim(claim_data)

        # Track initial workflow step
        await self.supabase.track_workflow_step(
            claim['id'],
            'claim_created',
            'completed',
            {
                'source': 'email',
                'sender': email_details['sender_email'],
                'attachments': len(email_details.get('attachments', []))
            }
        )

        # Store documents
        for attachment in email_details['attachments']:
            await self.supabase.store_claim_document(claim['id'], attachment)

        return claim
    
    async def _create_zendesk_ticket(self, claim_id: str, email_details: Dict):
        """Create Zendesk ticket for claim"""
        claim_data = await self.supabase.get_claim(claim_id)
        
        ticket_id = await self.zendesk.create_ticket(
            subject=f"[ARIA] {email_details['subject']}",
            description=email_details['body'],
            requester_email=email_details['sender_email'],
            requester_name=email_details['sender_name'],
            attachments=email_details['attachments'],
            custom_fields={
                'claim_number': claim_data['claim_number'],
                'ai_system': 'ARIA'
            },
            tags=['aria', 'automated', 'claim']
        )
        
        # Store Zendesk ticket information in database
        ticket_url = f"https://{self.settings.zendesk.subdomain}.zendesk.com/agent/tickets/{ticket_id}"
        await self.supabase.store_zendesk_ticket_info(claim_id, ticket_id, ticket_url)

        # Track workflow step
        await self.supabase.track_workflow_step(
            claim_id,
            'zendesk_ticket_created',
            'completed',
            {
                'ticket_id': ticket_id,
                'ticket_url': ticket_url
            }
        )

        return ticket_id
    
    async def _send_user_acknowledgment(self, claim_id: str, email_details: Dict):
        """Send acknowledgment to user"""
        claim_data = await self.supabase.get_claim(claim_id)
        tracking_link = f"{self.settings.app.base_url}/track/{claim_data['claim_number']}"
        
        await self.notifications.send_multi_channel_notification(
            user_email=email_details['sender_email'],
            user_phone=None,  # Extract from claim if available
            message_type="claim_received",
            context={
                'claim_number': claim_data['claim_number'],
                'tracking_link': tracking_link,
                'user_name': email_details['sender_name']
            }
        )
    
    async def _notify_slack_team(self, claim_id: str):
        """Notify Slack team of new claim"""
        claim_data = await self.supabase.get_claim(claim_id)
        await self.slack.send_new_claim_notification(claim_data)
    
    async def _start_document_processing(self, claim_id: str):
        """Start document processing workflow"""
        # This would trigger OCR processing
        # For now, we'll simulate with a delay
        await asyncio.sleep(1)
        await self.process_document_analysis_complete(claim_id)
    
    async def _assign_to_human_agent(self, claim_id: str, analysis_results: Dict):
        """Assign claim to human agent based on analysis"""
        # Find best available agent
        agent = await self.supabase.find_best_agent(
            claim_type=analysis_results['classification'].get('claim_type'),
            priority=analysis_results['overall'].get('processing_priority', 3)
        )
        
        if agent:
            await self.process_agent_assignment(claim_id, agent['email'])
        else:
            # No agent available, add to queue
            await self._update_claim_status(
                claim_id,
                WorkflowStatus.HUMAN_REVIEW,
                "Waiting for agent assignment"
            )
    
    async def _auto_process_claim(self, claim_id: str, analysis_results: Dict):
        """Process claim - ALL CLAIMS REQUIRE HUMAN APPROVAL"""
        # NO AUTO-APPROVAL: All claims must be reviewed by human agents
        # This ensures proper oversight and compliance

        logger.info(f"Claim {claim_id}: Assigning to human agent for mandatory review")

        # Always assign to human for review - no exceptions
        await self._assign_to_human_agent(claim_id, analysis_results)
    
    async def _process_approval(self, claim_id: str, decision_data: Dict):
        """Process claim approval"""
        await self._update_claim_status(
            claim_id,
            WorkflowStatus.APPROVED,
            f"Claim approved for ${decision_data.get('approved_amount', 'TBD')}"
        )
        
        # Notify user of approval
        await self._notify_user_decision(claim_id, 'approved', decision_data)
        
        # Close claim
        await self._close_claim(claim_id)
    
    async def _process_denial(self, claim_id: str, decision_data: Dict):
        """Process claim denial"""
        await self._update_claim_status(
            claim_id,
            WorkflowStatus.DENIED,
            f"Claim denied: {decision_data.get('denial_reason', 'See details')}"
        )
        
        # Notify user of denial
        await self._notify_user_decision(claim_id, 'denied', decision_data)
        
        # Close claim
        await self._close_claim(claim_id)
    
    async def _request_more_information(self, claim_id: str, decision_data: Dict):
        """Request more information from user"""
        await self._update_claim_status(
            claim_id,
            WorkflowStatus.MORE_INFO_REQUIRED,
            "Additional information requested"
        )
        
        # Send request to user
        await self._notify_user_info_request(claim_id, decision_data)
    
    async def _escalate_claim(self, claim_id: str, reason: str):
        """Escalate claim to senior team"""
        await self._update_claim_status(
            claim_id,
            WorkflowStatus.ESCALATED,
            f"Escalated: {reason}"
        )
        
        # Notify senior team
        claim_data = await self.supabase.get_claim(claim_id)
        await self.slack.send_urgent_alert(claim_data, reason)
    
    async def _close_claim(self, claim_id: str):
        """Close claim and finalize workflow"""
        await self._update_claim_status(
            claim_id,
            WorkflowStatus.CLOSED,
            "Claim processing completed"
        )
        
        # Update Zendesk ticket
        claim_data = await self.supabase.get_claim(claim_id)
        if claim_data.get('zendesk_ticket_id'):
            await self.zendesk.update_ticket_status(
                claim_data['zendesk_ticket_id'],
                'solved',
                'Claim processing completed via ARIA system'
            )
    
    async def _update_claim_status(self, claim_id: str, status: WorkflowStatus, message: str):
        """Update claim status and create timeline entry"""
        await self.supabase.update_claim_status(claim_id, status.value, message)

        # Log to Zendesk ticket
        await self._log_to_zendesk(claim_id, f"Status Update: {status.value}", message)

        # Notify user of status change
        claim_data = await self.supabase.get_claim(claim_id)
        await self._notify_user_status_update(claim_data, message)
    
    async def _notify_user_status_update(self, claim_data: Dict, message: str):
        """Notify user of status update"""
        tracking_link = f"{self.settings.app.base_url}/track/{claim_data['claim_number']}"
        
        await self.notifications.send_multi_channel_notification(
            user_email=claim_data['user_email'],
            user_phone=claim_data.get('user_phone'),
            message_type="status_update",
            context={
                'claim_number': claim_data['claim_number'],
                'new_status': claim_data['status'],
                'update_message': message,
                'tracking_link': tracking_link,
                'user_name': claim_data.get('user_name')
            }
        )
    
    async def _workflow_monitor(self):
        """Monitor active workflows for issues"""
        while self.is_running:
            try:
                # Check for stuck workflows
                stuck_claims = await self.supabase.get_stuck_claims()
                
                for claim in stuck_claims:
                    logger.warning(f"Stuck claim detected: {claim['claim_number']}")
                    await self._handle_stuck_claim(claim)
                
                await asyncio.sleep(300)  # Check every 5 minutes
                
            except Exception as e:
                logger.error(f"Error in workflow monitor: {e}")
                await asyncio.sleep(60)
    
    async def _sla_monitor(self):
        """Monitor SLA compliance"""
        while self.is_running:
            try:
                # Check SLA breaches
                sla_breaches = await self.supabase.get_sla_breaches()
                
                for claim in sla_breaches:
                    logger.warning(f"SLA breach detected: {claim['claim_number']}")
                    await self._handle_sla_breach(claim)
                
                await asyncio.sleep(600)  # Check every 10 minutes
                
            except Exception as e:
                logger.error(f"Error in SLA monitor: {e}")
                await asyncio.sleep(60)
    
    async def _handle_stuck_claim(self, claim: Dict):
        """Handle stuck claim workflow"""
        await self._escalate_claim(
            claim['id'],
            f"Workflow stuck in {claim['status']} for {claim['stuck_duration']} minutes"
        )
    
    async def _handle_sla_breach(self, claim: Dict):
        """Handle SLA breach"""
        await self.slack.send_urgent_alert(
            claim,
            f"SLA breach: {claim['sla_type']} exceeded by {claim['breach_duration']} minutes"
        )
    
    async def _log_to_zendesk(self, claim_id: str, title: str, message: str, is_public: bool = False):
        """Log workflow step to Zendesk ticket"""
        try:
            claim_data = await self.supabase.get_claim(claim_id)
            if claim_data and claim_data.get('zendesk_ticket_id'):
                timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S UTC")
                formatted_comment = f"[{timestamp}] {title}\n\n{message}\n\n---\nAutomated update from ARIA Claims Processing System"

                await self.zendesk.add_comment(
                    claim_data['zendesk_ticket_id'],
                    formatted_comment,
                    is_public=is_public
                )
                logger.info(f"Logged to Zendesk ticket {claim_data['zendesk_ticket_id']}: {title}")
        except Exception as e:
            logger.error(f"Error logging to Zendesk for claim {claim_id}: {e}")

    async def _log_agent_decision_to_zendesk(self, claim_id: str, decision_data: Dict):
        """Log detailed agent decision to Zendesk"""
        try:
            decision = decision_data.get('decision', 'unknown')
            agent_email = decision_data.get('agent_email', 'Unknown Agent')
            reasoning = decision_data.get('reasoning', 'No reasoning provided')

            title = f"Agent Decision: {decision.upper()}"

            message_parts = [
                f"Agent: {agent_email}",
                f"Decision: {decision}",
                f"Reasoning: {reasoning}"
            ]

            if decision == 'approve':
                approved_amount = decision_data.get('approved_amount', 'TBD')
                message_parts.append(f"Approved Amount: ${approved_amount}")
            elif decision == 'deny':
                denial_reason = decision_data.get('denial_reason', reasoning)
                message_parts.append(f"Denial Reason: {denial_reason}")
            elif decision == 'request_more_info':
                requested_info = decision_data.get('requested_info', 'Additional documentation required')
                message_parts.append(f"Information Requested: {requested_info}")

            confidence = decision_data.get('confidence_level', 'Not specified')
            message_parts.append(f"Agent Confidence Level: {confidence}")

            review_time = decision_data.get('review_time_minutes', 'Not tracked')
            message_parts.append(f"Review Time: {review_time} minutes")

            message = "\n".join(message_parts)

            await self._log_to_zendesk(claim_id, title, message, is_public=True)

        except Exception as e:
            logger.error(f"Error logging agent decision to Zendesk: {e}")

    async def _log_ai_analysis_to_zendesk(self, claim_id: str, analysis_results: Dict):
        """Log AI analysis results to Zendesk"""
        try:
            title = "AI Analysis Completed"

            message_parts = [
                "AI analysis has been completed for this claim.",
                "",
                "Analysis Summary:"
            ]

            # Add overall assessment
            overall = analysis_results.get('overall', {})
            if overall:
                message_parts.extend([
                    f"• Overall Confidence: {overall.get('confidence_score', 'N/A')}%",
                    f"• Processing Priority: {overall.get('processing_priority', 'Normal')}",
                    f"• Estimated Amount: ${overall.get('estimated_amount', 'TBD')}",
                    f"• Fraud Risk Score: {overall.get('fraud_risk_score', 'N/A')}%"
                ])

            # Add classification results
            classification = analysis_results.get('classification', {})
            if classification:
                message_parts.extend([
                    "",
                    "Classification:",
                    f"• Claim Type: {classification.get('claim_type', 'Unknown')}",
                    f"• Severity: {classification.get('severity', 'Unknown')}",
                    f"• Coverage Assessment: {classification.get('coverage_decision', 'Pending review')}"
                ])

            # Add document analysis summary
            documents = analysis_results.get('documents', [])
            if documents:
                message_parts.extend([
                    "",
                    f"Documents Analyzed: {len(documents)} files",
                    "• All documents have been processed and analyzed",
                    "• OCR extraction completed where applicable",
                    "• Key information extracted and validated"
                ])

            # Add next steps
            message_parts.extend([
                "",
                "Next Steps:",
                "• Claim requires human review (auto-approval disabled)",
                "• Will be assigned to available claims specialist",
                "• Agent will receive detailed analysis report",
                "• Customer will be notified of assignment"
            ])

            message = "\n".join(message_parts)

            await self._log_to_zendesk(claim_id, title, message, is_public=False)

        except Exception as e:
            logger.error(f"Error logging AI analysis to Zendesk: {e}")

    async def _handle_workflow_error(self, email_details: Dict, error: str):
        """Handle workflow errors"""
        logger.error(f"Workflow error for {email_details['sender_email']}: {error}")

        # Send error notification to user
        await self.notifications.send_email(
            to_email=email_details['sender_email'],
            subject="Issue Processing Your Claim Submission",
            template="processing_error",
            context={
                'sender_name': email_details['sender_name'],
                'error_message': "We encountered an issue processing your submission. Our team has been notified.",
                'support_email': self.settings.email.reply_to
            }
        )
