"""
ARIA Supabase Database Client
Complete database operations for claims processing
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import uuid

from supabase import create_client, Client
import asyncpg

from ..config import Settings

logger = logging.getLogger(__name__)


class SupabaseClient:
    """Supabase database client for ARIA claims processing"""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.client: Client = create_client(
            settings.database.supabase_url,
            settings.database.supabase_anon_key
        )
        self.service_client: Client = create_client(
            settings.database.supabase_url,
            settings.database.supabase_service_role_key
        )
    
    async def health_check(self) -> bool:
        """Check database connection health"""
        try:
            result = self.client.table('claims').select('id').limit(1).execute()
            return True
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            return False
    
    async def initialize_schema(self):
        """Initialize database schema"""
        try:
            # Read and execute schema file
            schema_file = self.settings.database.schema_file
            if schema_file and schema_file.exists():
                with open(schema_file, 'r') as f:
                    schema_sql = f.read()
                
                # Execute schema using direct SQL
                # Note: This would need to be done via Supabase SQL editor or API
                logger.info("Schema file found. Please execute the schema manually in Supabase SQL editor.")
                logger.info(f"Schema file location: {schema_file}")
                return True
            else:
                logger.warning("Schema file not found. Creating basic tables...")
                await self._create_basic_tables()
                return True
                
        except Exception as e:
            logger.error(f"Error initializing schema: {e}")
            return False
    
    async def _create_basic_tables(self):
        """Create basic tables if schema file not available"""
        # This is a simplified version - the full schema should be applied via SQL editor
        logger.info("Creating basic claims table structure...")
        # In a real implementation, we'd use Supabase's table creation API
        # For now, we'll assume the schema is applied manually
    
    async def create_claim(self, claim_data: Dict) -> Dict:
        """Create a new claim record"""
        try:
            # Generate claim number if not provided
            if 'claim_number' not in claim_data:
                claim_data['claim_number'] = await self._generate_claim_number()
            
            # Add timestamps
            claim_data['created_at'] = datetime.now().isoformat()
            claim_data['updated_at'] = datetime.now().isoformat()
            
            # Insert claim
            result = self.service_client.table('claims').insert(claim_data).execute()
            
            if result.data:
                logger.info(f"Created claim: {result.data[0]['claim_number']}")
                return result.data[0]
            else:
                raise Exception("No data returned from insert")
                
        except Exception as e:
            logger.error(f"Error creating claim: {e}")
            raise
    
    async def get_claim(self, claim_id: str) -> Optional[Dict]:
        """Get claim by ID"""
        try:
            result = self.client.table('claims').select('*').eq('id', claim_id).execute()
            
            if result.data:
                return result.data[0]
            return None
            
        except Exception as e:
            logger.error(f"Error getting claim {claim_id}: {e}")
            return None
    
    async def get_claim_by_number(self, claim_number: str) -> Optional[Dict]:
        """Get claim by claim number"""
        try:
            result = self.client.table('claims').select('*').eq('claim_number', claim_number).execute()
            
            if result.data:
                return result.data[0]
            return None
            
        except Exception as e:
            logger.error(f"Error getting claim by number {claim_number}: {e}")
            return None
    
    async def update_claim(self, claim_id: str, updates: Dict) -> bool:
        """Update claim record"""
        try:
            updates['updated_at'] = datetime.now().isoformat()
            
            result = self.service_client.table('claims').update(updates).eq('id', claim_id).execute()
            
            if result.data:
                logger.info(f"Updated claim {claim_id}")
                return True
            return False
            
        except Exception as e:
            logger.error(f"Error updating claim {claim_id}: {e}")
            return False
    
    async def update_claim_status(self, claim_id: str, status: str, message: str) -> bool:
        """Update claim status and create timeline entry"""
        try:
            # Update claim status
            await self.update_claim(claim_id, {'status': status})
            
            # Create timeline entry
            timeline_entry = {
                'claim_id': claim_id,
                'event_type': 'status_updated',
                'title': f'Status changed to {status}',
                'description': message,
                'actor_type': 'system',
                'created_at': datetime.now().isoformat()
            }
            
            await self.create_timeline_entry(timeline_entry)
            return True
            
        except Exception as e:
            logger.error(f"Error updating claim status: {e}")
            return False
    
    async def create_timeline_entry(self, timeline_data: Dict) -> bool:
        """Create timeline entry"""
        try:
            result = self.service_client.table('claim_timeline').insert(timeline_data).execute()
            return bool(result.data)

        except Exception as e:
            logger.error(f"Error creating timeline entry: {e}")
            return False

    async def add_timeline_event(
        self,
        claim_id: str,
        event_type: str,
        title: str,
        description: str,
        actor_type: str,
        actor_id: str = None,
        actor_name: str = None,
        actor_email: str = None,
        event_data: Dict = None
    ) -> bool:
        """Add a comprehensive timeline event"""
        try:
            timeline_entry = {
                'claim_id': claim_id,
                'event_type': event_type,
                'title': title,
                'description': description,
                'actor_type': actor_type,
                'actor_id': actor_id,
                'actor_name': actor_name,
                'actor_email': actor_email,
                'event_data': event_data or {},
                'created_at': datetime.now().isoformat()
            }

            return await self.create_timeline_entry(timeline_entry)

        except Exception as e:
            logger.error(f"Error adding timeline event: {e}")
            return False
    
    async def get_claim_timeline(self, claim_id: str) -> List[Dict]:
        """Get claim timeline"""
        try:
            result = self.client.table('claim_timeline').select('*').eq('claim_id', claim_id).order('created_at').execute()
            return result.data or []
            
        except Exception as e:
            logger.error(f"Error getting timeline for claim {claim_id}: {e}")
            return []
    
    async def store_claim_document(self, claim_id: str, document_data: Dict) -> bool:
        """Store claim document record"""
        try:
            document_data['claim_id'] = claim_id
            document_data['uploaded_at'] = datetime.now().isoformat()
            
            result = self.service_client.table('claim_documents').insert(document_data).execute()
            return bool(result.data)
            
        except Exception as e:
            logger.error(f"Error storing document: {e}")
            return False
    
    async def get_claim_documents(self, claim_id: str) -> List[Dict]:
        """Get documents for claim"""
        try:
            result = self.client.table('claim_documents').select('*').eq('claim_id', claim_id).execute()
            return result.data or []
            
        except Exception as e:
            logger.error(f"Error getting documents for claim {claim_id}: {e}")
            return []
    
    async def update_document_ocr(self, document_id: str, ocr_result: Dict) -> bool:
        """Update document with OCR results"""
        try:
            updates = {
                'ocr_text': ocr_result.get('text', ''),
                'ocr_confidence': ocr_result.get('confidence', 0.0),
                'extracted_data': ocr_result.get('entities', {}),
                'is_processed': True,
                'processed_at': datetime.now().isoformat()
            }
            
            result = self.service_client.table('claim_documents').update(updates).eq('id', document_id).execute()
            return bool(result.data)
            
        except Exception as e:
            logger.error(f"Error updating document OCR: {e}")
            return False
    
    async def store_ai_analysis(self, claim_id: str, analysis_results: Dict) -> bool:
        """Store AI analysis results"""
        try:
            analysis_data = {
                'claim_id': claim_id,
                'analysis_type': 'comprehensive',
                'model_version': 'gpt-4-turbo',
                'results': analysis_results,
                'confidence_score': analysis_results.get('overall', {}).get('overall_confidence', 0.0),
                'created_at': datetime.now().isoformat()
            }
            
            result = self.service_client.table('ai_analysis').insert(analysis_data).execute()
            return bool(result.data)
            
        except Exception as e:
            logger.error(f"Error storing AI analysis: {e}")
            return False
    
    async def assign_claim(self, claim_id: str, agent_email: str) -> bool:
        """Assign claim to agent"""
        try:
            # Get or create agent
            agent = await self._get_or_create_agent(agent_email)
            
            if agent:
                updates = {
                    'assigned_agent_id': agent['id'],
                    'status': 'human_review'
                }
                return await self.update_claim(claim_id, updates)
            
            return False
            
        except Exception as e:
            logger.error(f"Error assigning claim: {e}")
            return False
    
    async def store_agent_decision(self, claim_id: str, decision_data: Dict) -> bool:
        """Store agent decision"""
        try:
            decision_data['claim_id'] = claim_id
            decision_data['created_at'] = datetime.now().isoformat()
            
            result = self.service_client.table('agent_decisions').insert(decision_data).execute()
            return bool(result.data)
            
        except Exception as e:
            logger.error(f"Error storing agent decision: {e}")
            return False
    
    async def log_notification(self, notification_data: Dict) -> bool:
        """Log notification"""
        try:
            notification_data['created_at'] = datetime.now().isoformat()
            
            result = self.service_client.table('notifications').insert(notification_data).execute()
            return bool(result.data)
            
        except Exception as e:
            logger.error(f"Error logging notification: {e}")
            return False
    
    async def find_best_agent(self, claim_type: str = None, priority: int = 3) -> Optional[Dict]:
        """Find best available agent"""
        try:
            # Simple agent selection - in production this would be more sophisticated
            result = self.client.table('agents').select('*').eq('is_active', True).limit(1).execute()
            
            if result.data:
                return result.data[0]
            return None
            
        except Exception as e:
            logger.error(f"Error finding agent: {e}")
            return None
    
    async def get_stuck_claims(self) -> List[Dict]:
        """Get claims that are stuck in processing"""
        try:
            # Find claims that haven't been updated in the last 2 hours
            cutoff_time = (datetime.now() - timedelta(hours=2)).isoformat()

            result = self.client.table('claims').select('*').lt('updated_at', cutoff_time).in_('status', ['received', 'documents_processing', 'ai_analysis']).execute()

            return result.data or []

        except Exception as e:
            logger.error(f"Error getting stuck claims: {e}")
            return []

    async def get_sla_breaches(self) -> List[Dict]:
        """Get claims that have breached SLA"""
        try:
            # Find claims that have exceeded SLA timeframes
            initial_response_cutoff = (datetime.now() - timedelta(minutes=15)).isoformat()
            analysis_cutoff = (datetime.now() - timedelta(hours=2)).isoformat()
            decision_cutoff = (datetime.now() - timedelta(hours=4)).isoformat()

            # Check for initial response SLA breach
            result = self.client.table('claims').select('*').lt('created_at', initial_response_cutoff).eq('status', 'received').execute()

            sla_breaches = []
            for claim in result.data or []:
                claim['sla_type'] = 'initial_response'
                claim['breach_duration'] = self._calculate_breach_duration(claim['created_at'], 15)
                sla_breaches.append(claim)

            return sla_breaches

        except Exception as e:
            logger.error(f"Error getting SLA breaches: {e}")
            return []

    def _calculate_breach_duration(self, start_time: str, sla_minutes: int) -> int:
        """Calculate how many minutes SLA has been breached"""
        try:
            start = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
            elapsed = (datetime.now() - start).total_seconds() / 60
            return max(0, int(elapsed - sla_minutes))
        except:
            return 0

    async def track_workflow_step(
        self,
        claim_id: str,
        step_name: str,
        step_status: str,
        details: Dict = None,
        actor_email: str = None
    ) -> bool:
        """Track a workflow step with comprehensive logging"""
        try:
            # Update claim metadata with step tracking
            claim_data = await self.get_claim(claim_id)
            if not claim_data:
                return False

            # Update workflow tracking in metadata
            metadata = claim_data.get('metadata', {})
            workflow_steps = metadata.get('workflow_steps', {})

            workflow_steps[step_name] = {
                'status': step_status,
                'timestamp': datetime.now().isoformat(),
                'details': details or {},
                'actor': actor_email
            }

            metadata['workflow_steps'] = workflow_steps
            metadata['last_workflow_step'] = step_name
            metadata['last_workflow_update'] = datetime.now().isoformat()

            # Update claim
            await self.update_claim(claim_id, {'metadata': metadata})

            # Add timeline event
            await self.add_timeline_event(
                claim_id=claim_id,
                event_type='workflow_step',
                title=f"Workflow Step: {step_name}",
                description=f"Step '{step_name}' completed with status: {step_status}",
                actor_type='system' if not actor_email else 'agent',
                actor_email=actor_email,
                event_data={
                    'step_name': step_name,
                    'step_status': step_status,
                    'details': details or {}
                }
            )

            logger.info(f"Tracked workflow step '{step_name}' for claim {claim_id}")
            return True

        except Exception as e:
            logger.error(f"Error tracking workflow step: {e}")
            return False

    async def get_claim_statistics(self) -> Dict:
        """Get comprehensive claim statistics"""
        try:
            stats = {}

            # Total claims
            total_result = self.client.table('claims').select('id', count='exact').execute()
            stats['total_claims'] = total_result.count or 0

            # Claims by status
            status_result = self.client.table('claims').select('status').execute()
            status_counts = {}
            for claim in status_result.data or []:
                status = claim['status']
                status_counts[status] = status_counts.get(status, 0) + 1
            stats['by_status'] = status_counts

            # Recent claims (last 24 hours)
            recent_cutoff = (datetime.now() - timedelta(hours=24)).isoformat()
            recent_result = self.client.table('claims').select('id', count='exact').gte('created_at', recent_cutoff).execute()
            stats['recent_claims'] = recent_result.count or 0

            # Average processing time (for closed claims)
            closed_claims = self.client.table('claims').select('created_at', 'closed_at').not_.is_('closed_at', 'null').execute()
            processing_times = []
            for claim in closed_claims.data or []:
                try:
                    created = datetime.fromisoformat(claim['created_at'].replace('Z', '+00:00'))
                    closed = datetime.fromisoformat(claim['closed_at'].replace('Z', '+00:00'))
                    processing_time = (closed - created).total_seconds() / 3600  # hours
                    processing_times.append(processing_time)
                except:
                    continue

            if processing_times:
                stats['avg_processing_time_hours'] = sum(processing_times) / len(processing_times)
            else:
                stats['avg_processing_time_hours'] = 0

            return stats

        except Exception as e:
            logger.error(f"Error getting claim statistics: {e}")
            return {}

    async def store_zendesk_ticket_info(self, claim_id: str, ticket_id: int, ticket_url: str) -> bool:
        """Store Zendesk ticket information"""
        try:
            zendesk_data = {
                'claim_id': claim_id,
                'ticket_id': ticket_id,
                'ticket_url': ticket_url,
                'status': 'open',
                'created_at': datetime.now().isoformat(),
                'last_sync_at': datetime.now().isoformat(),
                'sync_status': 'synced'
            }

            result = self.service_client.table('zendesk_tickets').insert(zendesk_data).execute()

            if result.data:
                # Also update the claim with the ticket ID
                await self.update_claim(claim_id, {'zendesk_ticket_id': ticket_id})

                # Add timeline event
                await self.add_timeline_event(
                    claim_id=claim_id,
                    event_type='zendesk_ticket_created',
                    title="Zendesk Ticket Created",
                    description=f"Zendesk ticket {ticket_id} created for this claim",
                    actor_type='system',
                    event_data={
                        'ticket_id': ticket_id,
                        'ticket_url': ticket_url
                    }
                )

                return True

            return False

        except Exception as e:
            logger.error(f"Error storing Zendesk ticket info: {e}")
            return False

    async def update_claim_from_ticket(self, claim_id: str, ticket_info: Dict) -> bool:
        """Update claim based on Zendesk ticket information"""
        try:
            # Map Zendesk status to claim status
            status_mapping = {
                'new': 'received',
                'open': 'human_review',
                'pending': 'more_info_required',
                'solved': 'approved',
                'closed': 'closed'
            }

            zendesk_status = ticket_info.get('status', 'open')
            claim_status = status_mapping.get(zendesk_status, 'human_review')

            updates = {
                'status': claim_status,
                'updated_at': datetime.now().isoformat()
            }

            # Update metadata with Zendesk sync info
            claim_data = await self.get_claim(claim_id)
            if claim_data:
                metadata = claim_data.get('metadata', {})
                metadata['zendesk_sync'] = {
                    'last_sync': datetime.now().isoformat(),
                    'ticket_status': zendesk_status,
                    'ticket_updated_at': ticket_info.get('updated_at')
                }
                updates['metadata'] = metadata

            return await self.update_claim(claim_id, updates)

        except Exception as e:
            logger.error(f"Error updating claim from ticket: {e}")
            return False

    async def get_agent_workload(self, agent_email: str) -> Dict:
        """Get agent's current workload"""
        try:
            # Get agent
            agent_result = self.client.table('agents').select('*').eq('email', agent_email).execute()
            if not agent_result.data:
                return {}

            agent = agent_result.data[0]

            # Get active claims assigned to agent
            active_claims = self.client.table('claims').select('*').eq('assigned_agent_id', agent['id']).not_.in_('status', ['closed', 'denied']).execute()

            # Calculate workload metrics
            workload = {
                'agent_id': agent['id'],
                'agent_email': agent_email,
                'agent_name': agent.get('name', 'Unknown'),
                'active_claims_count': len(active_claims.data or []),
                'max_workload': agent.get('max_workload', 10),
                'current_workload': agent.get('current_workload', 0),
                'is_available': len(active_claims.data or []) < agent.get('max_workload', 10),
                'last_active': agent.get('last_active_at'),
                'specializations': agent.get('specializations', [])
            }

            # Add claim details
            workload['active_claims'] = []
            for claim in active_claims.data or []:
                workload['active_claims'].append({
                    'claim_number': claim['claim_number'],
                    'status': claim['status'],
                    'priority': claim.get('priority', 3),
                    'created_at': claim['created_at'],
                    'user_email': claim['user_email']
                })

            return workload

        except Exception as e:
            logger.error(f"Error getting agent workload: {e}")
            return {}
    
    async def get_sla_breaches(self) -> List[Dict]:
        """Get claims with SLA breaches"""
        try:
            # This would implement SLA breach detection
            # For now, return empty list
            return []
            
        except Exception as e:
            logger.error(f"Error getting SLA breaches: {e}")
            return []
    
    async def _generate_claim_number(self) -> str:
        """Generate unique claim number"""
        try:
            # Get current year
            year = datetime.now().year
            
            # Count claims for this year
            result = self.client.table('claims').select('id', count='exact').execute()
            count = result.count or 0
            
            # Generate claim number
            claim_number = f"CLAIM-{year}-{count + 1:06d}"
            return claim_number
            
        except Exception as e:
            logger.error(f"Error generating claim number: {e}")
            return f"CLAIM-{datetime.now().year}-{uuid.uuid4().hex[:6].upper()}"
    
    async def _get_or_create_agent(self, agent_email: str) -> Optional[Dict]:
        """Get or create agent record"""
        try:
            # Try to get existing agent
            result = self.client.table('agents').select('*').eq('email', agent_email).execute()
            
            if result.data:
                return result.data[0]
            
            # Create new agent
            agent_data = {
                'email': agent_email,
                'name': agent_email.split('@')[0].replace('.', ' ').title(),
                'team': 'Claims Processing',
                'role': 'Agent',
                'is_active': True,
                'created_at': datetime.now().isoformat()
            }
            
            result = self.service_client.table('agents').insert(agent_data).execute()
            
            if result.data:
                return result.data[0]
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting/creating agent: {e}")
            return None

    async def get_active_agents(self) -> List[Dict]:
        """Get all active agents"""
        try:
            result = self.client.table('agents').select('*').eq('is_active', True).execute()
            return result.data or []

        except Exception as e:
            logger.error(f"Error getting active agents: {e}")
            return []

    async def log_slack_notification(self, notification_data: Dict) -> bool:
        """Log Slack notification to database"""
        try:
            result = self.service_client.table('slack_notifications').insert(notification_data).execute()
            return bool(result.data)

        except Exception as e:
            logger.error(f"Error logging Slack notification: {e}")
            return False
