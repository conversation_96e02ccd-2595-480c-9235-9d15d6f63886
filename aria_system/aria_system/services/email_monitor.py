"""
ARIA Email Monitoring Service
Real-time email monitoring for claim submissions with attachment detection
"""

import asyncio
import email
import imaplib
import logging
import os
import re
from datetime import datetime
from email.mime.multipart import MI<PERSON>Multipart
from email.mime.text import MIMEText
from pathlib import Path
from typing import Dict, List, Optional, Tuple

import aiofiles
from email.header import decode_header
from email.utils import parseaddr

from ..config import Settings
from ..database.supabase_client import SupabaseClient
from ..services.zendesk_service import ZendeskService
from ..services.notification_service import NotificationService
from ..services.ai_classifier import ClaimClassifier

logger = logging.getLogger(__name__)


class EmailMonitor:
    """Real-time email monitoring service for claim processing"""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.supabase = SupabaseClient(settings)
        self.zendesk = ZendeskService(settings)
        self.notifications = NotificationService(settings)
        self.classifier = ClaimClassifier(settings)
        
        # Email configuration
        self.imap_server = settings.email.imap_server
        self.imap_port = settings.email.imap_port
        self.email_address = settings.email.claims_email
        self.email_password = settings.email.claims_password
        
        # Processing state
        self.is_running = False
        self.processed_emails = set()
        
        # Create attachments directory
        self.attachments_dir = Path("data/attachments")
        self.attachments_dir.mkdir(parents=True, exist_ok=True)
    
    async def start_monitoring(self):
        """Start the email monitoring service"""
        logger.info("Starting email monitoring service...")
        self.is_running = True
        
        while self.is_running:
            try:
                await self._check_emails()
                await asyncio.sleep(self.settings.email.check_interval)
            except Exception as e:
                logger.error(f"Error in email monitoring: {e}")
                await asyncio.sleep(60)  # Wait longer on error
    
    def stop_monitoring(self):
        """Stop the email monitoring service"""
        logger.info("Stopping email monitoring service...")
        self.is_running = False
    
    async def _check_emails(self):
        """Check for new emails and process them"""
        try:
            # Connect to IMAP server
            mail = imaplib.IMAP4_SSL(self.imap_server, self.imap_port)
            mail.login(self.email_address, self.email_password)
            mail.select('INBOX')
            
            # Search for unread emails
            status, messages = mail.search(None, 'UNSEEN')
            
            if status == 'OK' and messages[0]:
                email_ids = messages[0].split()
                logger.info(f"Found {len(email_ids)} new emails")
                
                for email_id in email_ids:
                    await self._process_email(mail, email_id)
            
            mail.close()
            mail.logout()
            
        except Exception as e:
            logger.error(f"Error checking emails: {e}")
    
    async def _process_email(self, mail: imaplib.IMAP4_SSL, email_id: bytes):
        """Process a single email"""
        try:
            # Fetch email
            status, msg_data = mail.fetch(email_id, '(RFC822)')
            if status != 'OK':
                return
            
            # Parse email
            email_message = email.message_from_bytes(msg_data[0][1])
            
            # Extract email details
            email_details = await self._extract_email_details(email_message)
            
            # Check if this is a claim-related email
            is_claim, confidence = await self.classifier.is_claim_email(
                email_details['subject'],
                email_details['body'],
                email_details['attachments']
            )
            
            if is_claim:
                await self._handle_claim_email(email_details)
            else:
                await self._handle_non_claim_email(email_details)
            
            # Mark as read
            mail.store(email_id, '+FLAGS', '\\Seen')
            
        except Exception as e:
            logger.error(f"Error processing email {email_id}: {e}")
    
    async def _extract_email_details(self, email_message) -> Dict:
        """Extract details from email message"""
        # Decode subject
        subject = ""
        if email_message["Subject"]:
            subject_parts = decode_header(email_message["Subject"])
            subject = "".join([
                part.decode(encoding or 'utf-8') if isinstance(part, bytes) else part
                for part, encoding in subject_parts
            ])
        
        # Extract sender
        from_header = email_message.get("From", "")
        sender_name, sender_email = parseaddr(from_header)
        
        # Extract body
        body = await self._extract_email_body(email_message)
        
        # Extract attachments
        attachments = await self._extract_attachments(email_message)
        
        return {
            'subject': subject,
            'sender_email': sender_email,
            'sender_name': sender_name,
            'body': body,
            'attachments': attachments,
            'received_at': datetime.now(),
            'message_id': email_message.get("Message-ID", ""),
            'raw_email': email_message
        }
    
    async def _extract_email_body(self, email_message) -> str:
        """Extract text body from email"""
        body = ""
        
        if email_message.is_multipart():
            for part in email_message.walk():
                content_type = part.get_content_type()
                content_disposition = str(part.get("Content-Disposition"))
                
                if content_type == "text/plain" and "attachment" not in content_disposition:
                    try:
                        body = part.get_payload(decode=True).decode('utf-8')
                        break
                    except:
                        continue
        else:
            try:
                body = email_message.get_payload(decode=True).decode('utf-8')
            except:
                body = str(email_message.get_payload())
        
        return body.strip()
    
    async def _extract_attachments(self, email_message) -> List[Dict]:
        """Extract and save attachments from email"""
        attachments = []
        
        if not email_message.is_multipart():
            return attachments
        
        for part in email_message.walk():
            content_disposition = str(part.get("Content-Disposition"))
            
            if "attachment" in content_disposition:
                filename = part.get_filename()
                if filename:
                    # Decode filename
                    if filename:
                        filename_parts = decode_header(filename)
                        filename = "".join([
                            part.decode(encoding or 'utf-8') if isinstance(part, bytes) else part
                            for part, encoding in filename_parts
                        ])
                    
                    # Save attachment
                    file_path = await self._save_attachment(part, filename)
                    
                    attachments.append({
                        'filename': filename,
                        'file_path': file_path,
                        'content_type': part.get_content_type(),
                        'size': len(part.get_payload(decode=True))
                    })
        
        return attachments
    
    async def _save_attachment(self, part, filename: str) -> str:
        """Save email attachment to disk"""
        # Generate unique filename
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        safe_filename = re.sub(r'[^\w\-_\.]', '_', filename)
        unique_filename = f"{timestamp}_{safe_filename}"
        
        file_path = self.attachments_dir / unique_filename
        
        # Save file
        async with aiofiles.open(file_path, 'wb') as f:
            await f.write(part.get_payload(decode=True))
        
        return str(file_path)
    
    async def _handle_claim_email(self, email_details: Dict):
        """Handle email identified as a claim"""
        logger.info(f"Processing claim email from {email_details['sender_email']}")

        try:
            # Check if email has sufficient attachments for claim processing
            has_sufficient_docs = await self._check_sufficient_documentation(email_details)

            if not has_sufficient_docs:
                logger.info(f"Insufficient documentation for claim from {email_details['sender_email']}")
                await self._request_attachments(email_details)
                return

            # Create claim in database first
            claim_data = await self._create_claim_record(email_details)
            logger.info(f"Created claim record: {claim_data['claim_number']}")

            # Create Zendesk ticket immediately
            ticket_id = await self.zendesk.create_ticket(
                subject=f"[{claim_data['claim_number']}] {email_details['subject']}",
                description=f"New claim submission from {email_details['sender_name']}\n\n{email_details['body']}",
                requester_email=email_details['sender_email'],
                requester_name=email_details['sender_name'],
                attachments=email_details['attachments']
            )

            # Update claim with Zendesk ticket ID
            await self.supabase.update_claim(
                claim_data['id'],
                {'zendesk_ticket_id': ticket_id}
            )

            # Log initial step in Zendesk
            await self.zendesk.add_comment(
                ticket_id,
                f"Claim {claim_data['claim_number']} created and processing started.\n"
                f"Status: Received\n"
                f"Attachments: {len(email_details['attachments'])} files\n"
                f"Next step: Document processing and AI analysis"
            )

            # Send acknowledgment to user with tracking link
            await self._send_claim_acknowledgment(claim_data, email_details)

            # Notify Slack team about new claim
            await self._notify_slack_team_new_claim(claim_data, email_details)

            # Trigger AI analysis
            await self._trigger_ai_analysis(claim_data)

            logger.info(f"Claim {claim_data['claim_number']} created successfully with Zendesk ticket {ticket_id}")

        except Exception as e:
            logger.error(f"Error handling claim email: {e}")
            await self._send_error_notification(email_details, str(e))
    
    async def _handle_non_claim_email(self, email_details: Dict):
        """Handle email not identified as a claim"""
        logger.info(f"Processing non-claim email from {email_details['sender_email']}")
        
        # Send helpful response with claim submission guidance
        await self._send_claim_guidance(email_details)
    
    async def _check_sufficient_documentation(self, email_details: Dict) -> bool:
        """Check if email has sufficient documentation to process claim"""
        attachments = email_details.get('attachments', [])

        # If no attachments, definitely insufficient
        if not attachments:
            return False

        # Check for minimum documentation requirements
        has_photos = any('jpg' in att.get('filename', '').lower() or
                        'jpeg' in att.get('filename', '').lower() or
                        'png' in att.get('filename', '').lower()
                        for att in attachments)

        has_documents = any('pdf' in att.get('filename', '').lower() or
                           'doc' in att.get('filename', '').lower()
                           for att in attachments)

        # For now, require at least one attachment (can be enhanced later)
        return len(attachments) >= 1

    async def _request_attachments(self, email_details: Dict):
        """Request attachments from user for claim processing"""
        logger.info(f"Requesting attachments from {email_details['sender_email']}")

        # Generate upload link
        upload_token = await self._generate_upload_token(email_details['sender_email'])
        upload_link = f"{self.settings.app.base_url}/upload/{upload_token}"

        # Send email with upload instructions using the new template
        await self.notifications.send_email(
            to_email=email_details['sender_email'],
            subject="Additional Documents Required for Your Claim",
            template="request_attachments_email",
            context={
                'sender_name': email_details['sender_name'],
                'upload_link': upload_link,
                'original_subject': email_details['subject']
            }
        )
    
    async def _create_claim_record(self, email_details: Dict) -> Dict:
        """Create claim record in database"""
        # Convert datetime to ISO string if it's a datetime object
        received_at = email_details['received_at']
        if hasattr(received_at, 'isoformat'):
            received_at = received_at.isoformat()

        claim_data = {
            'user_email': email_details['sender_email'],
            'user_name': email_details['sender_name'],
            'subject': email_details['subject'],
            'description': email_details['body'],
            'status': 'received',
            'reported_date': received_at,
            'metadata': {
                'source': 'email',
                'message_id': email_details['message_id'],
                'attachment_count': len(email_details['attachments'])
            }
        }

        return await self.supabase.create_claim(claim_data)
    
    async def _send_claim_acknowledgment(self, claim_data: Dict, email_details: Dict):
        """Send claim acknowledgment to user"""
        tracking_link = f"{self.settings.app.base_url}/track/{claim_data['claim_number']}"
        
        # Multi-channel acknowledgment
        await self.notifications.send_multi_channel_notification(
            user_email=email_details['sender_email'],
            user_phone=None,  # Will be extracted from claim if available
            message_type="claim_received",
            context={
                'claim_number': claim_data['claim_number'],
                'tracking_link': tracking_link,
                'user_name': email_details['sender_name']
            }
        )
    
    async def _trigger_ai_analysis(self, claim_data: Dict):
        """Trigger AI analysis of the claim"""
        # This will be implemented in the AI analysis service
        logger.info(f"Triggering AI analysis for claim {claim_data['claim_number']}")
        # TODO: Implement AI analysis trigger
    
    async def _notify_slack_team_new_claim(self, claim_data: Dict, email_details: Dict):
        """Notify Slack team about new claim"""
        try:
            message = f"🚨 **New Claim Received**\n" \
                     f"**Claim Number:** {claim_data['claim_number']}\n" \
                     f"**Customer:** {email_details['sender_name']} ({email_details['sender_email']})\n" \
                     f"**Subject:** {email_details['subject']}\n" \
                     f"**Attachments:** {len(email_details.get('attachments', []))} files\n" \
                     f"**Status:** Processing started\n" \
                     f"**Zendesk Ticket:** {claim_data.get('zendesk_ticket_id', 'Creating...')}"

            # Send to specific claims channel
            await self.notifications.send_slack_notification(
                channel=self.settings.slack.claims_channel,
                message=message
            )

            logger.info(f"Notified Slack team about new claim {claim_data['claim_number']}")

        except Exception as e:
            logger.error(f"Error notifying Slack team: {e}")

    async def _send_claim_guidance(self, email_details: Dict):
        """Send claim submission guidance to user"""
        upload_link = f"{self.settings.app.base_url}/submit-claim"

        await self.notifications.send_email(
            to_email=email_details['sender_email'],
            subject="How to Submit Your Insurance Claim",
            template="claim_guidance_email",
            context={
                'sender_name': email_details['sender_name'],
                'upload_link': upload_link
            }
        )
    
    async def _send_error_notification(self, email_details: Dict, error: str):
        """Send error notification to user"""
        await self.notifications.send_email(
            to_email=email_details['sender_email'],
            subject="Issue Processing Your Claim Submission",
            template="processing_error",
            context={
                'sender_name': email_details['sender_name'],
                'error_message': "We encountered an issue processing your submission. Our team has been notified.",
                'support_email': self.settings.email.reply_to
            }
        )
    
    async def _generate_upload_token(self, email: str) -> str:
        """Generate secure upload token for user"""
        # TODO: Implement secure token generation
        import uuid
        return str(uuid.uuid4())
