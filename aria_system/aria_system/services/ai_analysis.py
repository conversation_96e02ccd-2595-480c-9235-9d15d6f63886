"""
ARIA AI Document Analysis Service
Comprehensive claim analysis using Zurich OCR API and OpenAI
"""

import asyncio
import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple

import aiohttp
import aiofiles
from openai import AsyncOpenAI

from ..config import Settings
from ..database.supabase_client import SupabaseClient

logger = logging.getLogger(__name__)


class AIAnalysisService:
    """AI-powered document analysis and claim processing"""
    
    def __init__(self, settings: Settings):
        self.settings = settings
        self.supabase = SupabaseClient(settings)
        
        # Initialize OpenAI client
        self.openai_client = AsyncOpenAI(
            api_key=settings.ai.openai_api_key
        )
        
        # Zurich OCR API configuration
        self.ocr_api_url = settings.ai.zurich_ocr_api_url
        self.ocr_api_key = settings.ai.zurich_ocr_api_key
        
        # Analysis prompts
        self.analysis_prompts = self._load_analysis_prompts()
    
    def _load_analysis_prompts(self) -> Dict[str, str]:
        """Load AI analysis prompts"""
        return {
            "claim_classification": """
            Analyze this insurance claim and provide a comprehensive assessment.
            
            Claim Information:
            - Subject: {subject}
            - Description: {description}
            - OCR Text: {ocr_text}
            
            Please provide analysis in JSON format:
            {{
                "claim_type": "auto|property|liability|medical|other",
                "incident_type": "collision|theft|fire|flood|injury|other",
                "severity": "low|medium|high|critical",
                "estimated_amount": number,
                "confidence_score": 0.0-1.0,
                "key_facts": ["fact1", "fact2", ...],
                "risk_factors": ["risk1", "risk2", ...],
                "required_documents": ["doc1", "doc2", ...],
                "recommendations": ["rec1", "rec2", ...],
                "fraud_indicators": ["indicator1", "indicator2", ...],
                "next_steps": ["step1", "step2", ...]
            }}
            """,
            
            "document_extraction": """
            Extract structured information from this insurance document OCR text.
            
            OCR Text: {ocr_text}
            Document Type: {document_type}
            
            Extract the following information in JSON format:
            {{
                "policy_number": "string",
                "claim_number": "string", 
                "incident_date": "YYYY-MM-DD",
                "incident_time": "HH:MM",
                "incident_location": "string",
                "parties_involved": [
                    {{
                        "name": "string",
                        "role": "claimant|witness|other_driver|police_officer",
                        "contact": "string"
                    }}
                ],
                "vehicle_details": [
                    {{
                        "make": "string",
                        "model": "string", 
                        "year": "number",
                        "license_plate": "string",
                        "vin": "string",
                        "damage_description": "string"
                    }}
                ],
                "monetary_amounts": [
                    {{
                        "type": "repair|medical|property|total",
                        "amount": number,
                        "currency": "USD"
                    }}
                ],
                "dates": [
                    {{
                        "type": "incident|report|medical_treatment",
                        "date": "YYYY-MM-DD"
                    }}
                ],
                "addresses": ["address1", "address2", ...],
                "phone_numbers": ["phone1", "phone2", ...],
                "key_statements": ["statement1", "statement2", ...]
            }}
            """,
            
            "fraud_detection": """
            Analyze this claim for potential fraud indicators.
            
            Claim Data: {claim_data}
            OCR Text: {ocr_text}
            
            Provide fraud analysis in JSON format:
            {{
                "fraud_risk_score": 0.0-1.0,
                "risk_level": "low|medium|high|critical",
                "fraud_indicators": [
                    {{
                        "indicator": "string",
                        "severity": "low|medium|high",
                        "description": "string"
                    }}
                ],
                "red_flags": ["flag1", "flag2", ...],
                "verification_needed": ["item1", "item2", ...],
                "recommended_actions": ["action1", "action2", ...],
                "investigation_priority": "low|medium|high|urgent"
            }}
            """
        }
    
    async def analyze_claim_documents(self, claim_id: str) -> Dict[str, Any]:
        """Perform comprehensive analysis of claim documents"""
        try:
            logger.info(f"Starting AI analysis for claim {claim_id}")
            
            # Get claim data and documents
            claim_data = await self.supabase.get_claim(claim_id)
            documents = await self.supabase.get_claim_documents(claim_id)
            
            if not claim_data:
                raise ValueError(f"Claim {claim_id} not found")
            
            # Process documents with OCR
            ocr_results = []
            for doc in documents:
                if not doc.get('is_processed'):
                    ocr_result = await self._process_document_ocr(doc)
                    if ocr_result:
                        ocr_results.append(ocr_result)
                        # Update document with OCR results
                        await self.supabase.update_document_ocr(doc['id'], ocr_result)
            
            # Combine all OCR text
            combined_ocr_text = "\n\n".join([
                result.get('text', '') for result in ocr_results
            ])
            
            # Perform AI analysis
            analysis_results = {}
            
            # 1. Claim classification and assessment
            classification = await self._classify_claim(
                claim_data, combined_ocr_text
            )
            analysis_results['classification'] = classification
            
            # 2. Document information extraction
            extraction = await self._extract_document_information(
                combined_ocr_text, documents
            )
            analysis_results['extraction'] = extraction
            
            # 3. Fraud detection analysis
            fraud_analysis = await self._analyze_fraud_risk(
                claim_data, combined_ocr_text
            )
            analysis_results['fraud_analysis'] = fraud_analysis
            
            # 4. Calculate overall confidence and recommendations
            overall_analysis = await self._generate_overall_analysis(
                claim_data, analysis_results
            )
            analysis_results['overall'] = overall_analysis

            # 5. Generate agent guidance and recommendations
            agent_guidance = await self._generate_agent_guidance(
                claim_data, analysis_results
            )
            analysis_results['agent_guidance'] = agent_guidance

            # 6. Create comprehensive summary for sharing
            analysis_summary = self._create_analysis_summary(
                claim_data, analysis_results
            )
            analysis_results['summary'] = analysis_summary

            # Store analysis results
            await self.supabase.store_ai_analysis(claim_id, analysis_results)

            # Update claim with analysis scores
            await self._update_claim_scores(claim_id, analysis_results)

            # Share results with agents
            await self._share_results_with_agents(claim_id, analysis_results)

            logger.info(f"Completed AI analysis for claim {claim_id}")
            return analysis_results
        
        except Exception as e:
            logger.error(f"Error in AI analysis for claim {claim_id}: {e}")
            raise
    
    async def _process_document_ocr(self, document: Dict) -> Optional[Dict]:
        """Process document with Zurich OCR API"""
        try:
            file_path = Path(document['file_path'])
            if not file_path.exists():
                logger.warning(f"Document file not found: {file_path}")
                return None
            
            # Prepare OCR request
            async with aiofiles.open(file_path, 'rb') as f:
                file_data = await f.read()
            
            # OCR configuration
            config = {
                "ocr_engine": "google",
                "google_processor": "OCR_PROCESSOR",
                "llm_routing_enabled": True,
                "post_processing": "v2",
                "preprocessing": "none",
                "parallel_processing": True
            }
            
            # Prepare multipart form data
            form_data = aiohttp.FormData()
            form_data.add_field(
                'files',
                file_data,
                filename=document['filename'],
                content_type=document.get('mime_type', 'application/octet-stream')
            )
            form_data.add_field('config', json.dumps(config))
            
            # Send OCR request
            headers = {}
            if self.ocr_api_key:
                headers['Authorization'] = f'Bearer {self.ocr_api_key}'
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    self.ocr_api_url,
                    data=form_data,
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=300)  # 5 minutes timeout
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        logger.info(f"OCR completed for {document['filename']}")
                        return self._process_ocr_response(result)
                    else:
                        error_text = await response.text()
                        logger.error(f"OCR failed for {document['filename']}: {response.status} - {error_text}")
                        return None
        
        except Exception as e:
            logger.error(f"Error processing OCR for {document['filename']}: {e}")
            return None
    
    def _process_ocr_response(self, ocr_response: Dict) -> Dict:
        """Process and normalize OCR API response"""
        try:
            # Extract text and confidence from OCR response
            # Enhanced processing for Zurich OCR API response format

            if 'results' in ocr_response:
                results = ocr_response['results']
                if results and len(results) > 0:
                    first_result = results[0]

                    # Extract structured entities and key-value pairs
                    entities = self._extract_entities(first_result)
                    key_values = self._extract_key_values(first_result)

                    return {
                        'text': first_result.get('extracted_text', ''),
                        'confidence': first_result.get('confidence', 0.0),
                        'entities': entities,
                        'key_values': key_values,
                        'structured_data': self._structure_insurance_data(entities, key_values),
                        'metadata': {
                            'processing_time': ocr_response.get('processing_time'),
                            'pages_processed': len(results),
                            'ocr_engine': ocr_response.get('engine', 'google'),
                            'raw_response': ocr_response
                        }
                    }
            
            # Fallback for different response formats
            return {
                'text': str(ocr_response.get('text', '')),
                'confidence': float(ocr_response.get('confidence', 0.0)),
                'entities': ocr_response.get('entities', []),
                'metadata': {'raw_response': ocr_response}
            }
        
        except Exception as e:
            logger.error(f"Error processing OCR response: {e}")
            return {
                'text': '',
                'confidence': 0.0,
                'entities': [],
                'metadata': {'error': str(e)}
            }
    
    async def _classify_claim(self, claim_data: Dict, ocr_text: str) -> Dict:
        """Classify and assess the claim using AI"""
        try:
            prompt = self.analysis_prompts['claim_classification'].format(
                subject=claim_data.get('subject', ''),
                description=claim_data.get('description', ''),
                ocr_text=ocr_text[:4000]  # Limit text length
            )
            
            response = await self.openai_client.chat.completions.create(
                model=self.settings.ai.openai_model,
                messages=[
                    {"role": "system", "content": "You are an expert insurance claim analyst. Provide accurate, detailed analysis in valid JSON format."},
                    {"role": "user", "content": prompt}
                ],
                temperature=self.settings.ai.openai_temperature,
                max_tokens=self.settings.ai.openai_max_tokens
            )
            
            result_text = response.choices[0].message.content
            
            # Parse JSON response
            try:
                return json.loads(result_text)
            except json.JSONDecodeError:
                # Extract JSON from response if wrapped in other text
                import re
                json_match = re.search(r'\{.*\}', result_text, re.DOTALL)
                if json_match:
                    return json.loads(json_match.group())
                else:
                    raise ValueError("No valid JSON found in response")
        
        except Exception as e:
            logger.error(f"Error in claim classification: {e}")
            return {
                "claim_type": "unknown",
                "confidence_score": 0.0,
                "error": str(e)
            }
    
    async def _extract_document_information(self, ocr_text: str, documents: List[Dict]) -> Dict:
        """Extract structured information from documents"""
        try:
            # Determine primary document type
            doc_type = "general"
            if documents:
                doc_type = documents[0].get('document_type', 'general')
            
            prompt = self.analysis_prompts['document_extraction'].format(
                ocr_text=ocr_text[:4000],
                document_type=doc_type
            )
            
            response = await self.openai_client.chat.completions.create(
                model=self.settings.ai.openai_model,
                messages=[
                    {"role": "system", "content": "You are an expert at extracting structured information from insurance documents. Return valid JSON only."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.1,  # Lower temperature for extraction
                max_tokens=2000
            )
            
            result_text = response.choices[0].message.content
            
            try:
                return json.loads(result_text)
            except json.JSONDecodeError:
                import re
                json_match = re.search(r'\{.*\}', result_text, re.DOTALL)
                if json_match:
                    return json.loads(json_match.group())
                else:
                    return {"error": "Failed to parse extraction results"}
        
        except Exception as e:
            logger.error(f"Error in document extraction: {e}")
            return {"error": str(e)}
    
    async def _analyze_fraud_risk(self, claim_data: Dict, ocr_text: str) -> Dict:
        """Analyze claim for fraud risk"""
        try:
            prompt = self.analysis_prompts['fraud_detection'].format(
                claim_data=json.dumps(claim_data, default=str),
                ocr_text=ocr_text[:3000]
            )
            
            response = await self.openai_client.chat.completions.create(
                model=self.settings.ai.openai_model,
                messages=[
                    {"role": "system", "content": "You are a fraud detection expert for insurance claims. Analyze carefully and provide detailed risk assessment in JSON format."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.2,
                max_tokens=1500
            )
            
            result_text = response.choices[0].message.content
            
            try:
                return json.loads(result_text)
            except json.JSONDecodeError:
                import re
                json_match = re.search(r'\{.*\}', result_text, re.DOTALL)
                if json_match:
                    return json.loads(json_match.group())
                else:
                    return {"fraud_risk_score": 0.0, "risk_level": "unknown"}
        
        except Exception as e:
            logger.error(f"Error in fraud analysis: {e}")
            return {"fraud_risk_score": 0.0, "error": str(e)}
    
    async def _generate_overall_analysis(self, claim_data: Dict, analysis_results: Dict) -> Dict:
        """Generate overall analysis and recommendations"""
        try:
            classification = analysis_results.get('classification', {})
            extraction = analysis_results.get('extraction', {})
            fraud = analysis_results.get('fraud_analysis', {})
            
            # Calculate overall confidence
            confidence_scores = [
                classification.get('confidence_score', 0.0),
                fraud.get('fraud_risk_score', 0.0)  # Inverse for fraud
            ]
            overall_confidence = sum(confidence_scores) / len(confidence_scores)
            
            # Determine complexity
            complexity_factors = [
                len(extraction.get('parties_involved', [])),
                len(extraction.get('vehicle_details', [])),
                len(fraud.get('fraud_indicators', [])),
                1 if classification.get('severity') in ['high', 'critical'] else 0
            ]
            complexity_score = min(1.0, sum(complexity_factors) / 10.0)
            
            # Generate recommendations
            recommendations = []
            
            if fraud.get('fraud_risk_score', 0.0) > 0.7:
                recommendations.append("Immediate fraud investigation required")
            elif fraud.get('fraud_risk_score', 0.0) > 0.4:
                recommendations.append("Enhanced verification recommended")
            
            if classification.get('severity') == 'critical':
                recommendations.append("Escalate to senior adjuster immediately")
            
            if overall_confidence < 0.6:
                recommendations.append("Request additional documentation")
            
            return {
                'overall_confidence': overall_confidence,
                'complexity_score': complexity_score,
                'processing_priority': self._calculate_priority(classification, fraud),
                'estimated_processing_time': self._estimate_processing_time(complexity_score, fraud.get('fraud_risk_score', 0.0)),
                'recommendations': recommendations,
                'next_steps': self._generate_next_steps(analysis_results),
                'human_review_required': self._requires_human_review(analysis_results)
            }
        
        except Exception as e:
            logger.error(f"Error generating overall analysis: {e}")
            return {
                'overall_confidence': 0.0,
                'complexity_score': 1.0,
                'error': str(e)
            }
    
    def _calculate_priority(self, classification: Dict, fraud: Dict) -> int:
        """Calculate processing priority (1=urgent, 4=low)"""
        if fraud.get('fraud_risk_score', 0.0) > 0.8:
            return 1
        elif classification.get('severity') == 'critical':
            return 1
        elif classification.get('severity') == 'high':
            return 2
        elif fraud.get('fraud_risk_score', 0.0) > 0.5:
            return 2
        else:
            return 3
    
    def _estimate_processing_time(self, complexity: float, fraud_risk: float) -> int:
        """Estimate processing time in minutes"""
        base_time = 30
        complexity_factor = complexity * 60
        fraud_factor = fraud_risk * 120
        
        return int(base_time + complexity_factor + fraud_factor)
    
    def _generate_next_steps(self, analysis_results: Dict) -> List[str]:
        """Generate next steps based on analysis"""
        steps = []
        
        classification = analysis_results.get('classification', {})
        fraud = analysis_results.get('fraud_analysis', {})
        
        if fraud.get('fraud_risk_score', 0.0) > 0.6:
            steps.append("Conduct fraud investigation")
        
        if classification.get('confidence_score', 0.0) < 0.7:
            steps.append("Request additional documentation")
        
        steps.append("Assign to qualified adjuster")
        steps.append("Verify policy coverage")
        steps.append("Contact claimant for clarification if needed")
        
        return steps
    
    def _requires_human_review(self, analysis_results: Dict) -> bool:
        """Determine if human review is required - ALWAYS TRUE"""
        # ALL CLAIMS REQUIRE HUMAN REVIEW - NO AUTO-APPROVAL
        # This ensures proper oversight and compliance for all claims
        return True
    
    async def _update_claim_scores(self, claim_id: str, analysis_results: Dict):
        """Update claim with analysis scores"""
        try:
            overall = analysis_results.get('overall', {})
            fraud = analysis_results.get('fraud_analysis', {})
            classification = analysis_results.get('classification', {})
            
            updates = {
                'ai_confidence_score': overall.get('overall_confidence', 0.0),
                'fraud_risk_score': fraud.get('fraud_risk_score', 0.0),
                'complexity_score': overall.get('complexity_score', 0.0),
                'priority': self._calculate_priority(classification, fraud),
                'estimated_amount': classification.get('estimated_amount'),
                'status': 'human_review' if overall.get('human_review_required') else 'ai_analysis'
            }
            
            await self.supabase.update_claim(claim_id, updates)

    def _extract_entities(self, ocr_result: Dict) -> List[Dict]:
        """Extract and categorize entities from OCR result"""
        entities = []

        # Extract from OCR entities if available
        raw_entities = ocr_result.get('entities', [])
        for entity in raw_entities:
            entity_type = entity.get('type', '').lower()
            entity_text = entity.get('text', '')
            confidence = entity.get('confidence', 0.0)

            # Categorize insurance-relevant entities
            if entity_type in ['date', 'datetime']:
                entities.append({
                    'type': 'date',
                    'text': entity_text,
                    'confidence': confidence,
                    'category': 'temporal'
                })
            elif entity_type in ['money', 'currency', 'number']:
                entities.append({
                    'type': 'amount',
                    'text': entity_text,
                    'confidence': confidence,
                    'category': 'financial'
                })
            elif entity_type in ['person', 'name']:
                entities.append({
                    'type': 'person',
                    'text': entity_text,
                    'confidence': confidence,
                    'category': 'identity'
                })
            elif entity_type in ['location', 'address']:
                entities.append({
                    'type': 'location',
                    'text': entity_text,
                    'confidence': confidence,
                    'category': 'location'
                })

        return entities

    def _extract_key_values(self, ocr_result: Dict) -> Dict:
        """Extract key-value pairs from OCR result"""
        key_values = {}

        # Extract from OCR key-value pairs if available
        raw_kvp = ocr_result.get('key_value_pairs', [])
        for kvp in raw_kvp:
            key = kvp.get('key', '').strip().lower()
            value = kvp.get('value', '').strip()
            confidence = kvp.get('confidence', 0.0)

            if key and value and confidence > 0.5:  # Only include high-confidence pairs
                key_values[key] = {
                    'value': value,
                    'confidence': confidence
                }

        return key_values

    def _structure_insurance_data(self, entities: List[Dict], key_values: Dict) -> Dict:
        """Structure extracted data into insurance-relevant categories"""
        structured = {
            'policy_info': {},
            'incident_details': {},
            'financial_info': {},
            'parties_involved': {},
            'dates': {},
            'locations': {}
        }

        # Process entities
        for entity in entities:
            category = entity.get('category', '')
            entity_type = entity.get('type', '')
            text = entity.get('text', '')

            if category == 'financial':
                if 'amount' not in structured['financial_info']:
                    structured['financial_info']['amounts'] = []
                structured['financial_info']['amounts'].append({
                    'type': entity_type,
                    'value': text,
                    'confidence': entity.get('confidence', 0.0)
                })
            elif category == 'temporal':
                if 'incident_dates' not in structured['dates']:
                    structured['dates']['incident_dates'] = []
                structured['dates']['incident_dates'].append({
                    'date': text,
                    'confidence': entity.get('confidence', 0.0)
                })
            elif category == 'identity':
                if 'people' not in structured['parties_involved']:
                    structured['parties_involved']['people'] = []
                structured['parties_involved']['people'].append({
                    'name': text,
                    'confidence': entity.get('confidence', 0.0)
                })
            elif category == 'location':
                if 'addresses' not in structured['locations']:
                    structured['locations']['addresses'] = []
                structured['locations']['addresses'].append({
                    'address': text,
                    'confidence': entity.get('confidence', 0.0)
                })

        # Process key-value pairs
        for key, data in key_values.items():
            value = data['value']
            confidence = data['confidence']

            # Map common insurance fields
            if 'policy' in key or 'number' in key:
                structured['policy_info']['policy_number'] = {
                    'value': value,
                    'confidence': confidence
                }
            elif 'claim' in key and 'number' in key:
                structured['policy_info']['claim_number'] = {
                    'value': value,
                    'confidence': confidence
                }
            elif 'deductible' in key:
                structured['financial_info']['deductible'] = {
                    'value': value,
                    'confidence': confidence
                }
            elif 'premium' in key:
                structured['financial_info']['premium'] = {
                    'value': value,
                    'confidence': confidence
                }

        return structured

    async def _generate_agent_guidance(self, claim_data: Dict, analysis_results: Dict) -> Dict:
        """Generate comprehensive guidance for agents"""
        try:
            classification = analysis_results.get('classification', {})
            fraud_analysis = analysis_results.get('fraud_analysis', {})
            overall = analysis_results.get('overall', {})

            guidance = {
                'decision_factors': [],
                'red_flags': [],
                'verification_steps': [],
                'documentation_review': [],
                'next_actions': [],
                'approval_guidance': {},
                'escalation_triggers': []
            }

            # Decision factors
            confidence = overall.get('overall_confidence', 0.0)
            if confidence > 0.8:
                guidance['decision_factors'].append("High AI confidence - strong supporting evidence")
            elif confidence < 0.5:
                guidance['decision_factors'].append("Low AI confidence - requires careful manual review")

            estimated_amount = classification.get('estimated_amount', 0)
            if estimated_amount > 50000:
                guidance['decision_factors'].append("High-value claim - requires senior approval")

            # Red flags from fraud analysis
            fraud_indicators = fraud_analysis.get('fraud_indicators', [])
            for indicator in fraud_indicators:
                guidance['red_flags'].append(f"Fraud risk: {indicator}")

            # Verification steps
            claim_type = classification.get('claim_type', 'unknown')
            if claim_type == 'auto':
                guidance['verification_steps'].extend([
                    "Verify police report details match claim description",
                    "Check vehicle registration and ownership",
                    "Validate repair estimates against market rates"
                ])
            elif claim_type == 'property':
                guidance['verification_steps'].extend([
                    "Verify property ownership and insurance coverage",
                    "Check weather reports for claimed incident date",
                    "Validate contractor estimates and credentials"
                ])

            # Documentation review
            required_docs = classification.get('required_documents', [])
            for doc in required_docs:
                guidance['documentation_review'].append(f"Review: {doc}")

            # Next actions based on analysis
            if overall.get('human_review_required', True):
                guidance['next_actions'].append("Conduct thorough manual review")

            if fraud_analysis.get('fraud_risk_score', 0.0) > 0.7:
                guidance['next_actions'].append("Initiate fraud investigation")
                guidance['escalation_triggers'].append("High fraud risk detected")

            # Approval guidance
            guidance['approval_guidance'] = {
                'recommended_action': self._get_recommended_action(analysis_results),
                'confidence_level': confidence,
                'risk_assessment': fraud_analysis.get('fraud_risk_score', 0.0),
                'complexity_score': overall.get('complexity_score', 0.0),
                'estimated_processing_time': overall.get('estimated_processing_time', '2-4 hours')
            }

            return guidance

        except Exception as e:
            logger.error(f"Error generating agent guidance: {e}")
            return {
                'error': str(e),
                'next_actions': ['Manual review required due to analysis error']
            }

    def _get_recommended_action(self, analysis_results: Dict) -> str:
        """Get recommended action based on analysis"""
        overall = analysis_results.get('overall', {})
        fraud = analysis_results.get('fraud_analysis', {})
        classification = analysis_results.get('classification', {})

        confidence = overall.get('overall_confidence', 0.0)
        fraud_risk = fraud.get('fraud_risk_score', 0.0)
        estimated_amount = classification.get('estimated_amount', 0)

        if fraud_risk > 0.8:
            return "deny_investigate_fraud"
        elif confidence < 0.4:
            return "request_more_information"
        elif estimated_amount > 100000:
            return "escalate_high_value"
        elif confidence > 0.8 and fraud_risk < 0.3:
            return "approve_with_confidence"
        else:
            return "review_and_decide"

    def _create_analysis_summary(self, claim_data: Dict, analysis_results: Dict) -> Dict:
        """Create comprehensive analysis summary for sharing"""
        classification = analysis_results.get('classification', {})
        fraud_analysis = analysis_results.get('fraud_analysis', {})
        overall = analysis_results.get('overall', {})
        extraction = analysis_results.get('extraction', {})

        return {
            'claim_overview': {
                'claim_number': claim_data.get('claim_number'),
                'claim_type': classification.get('claim_type', 'Unknown'),
                'incident_type': classification.get('incident_type', 'Unknown'),
                'severity': classification.get('severity', 'Unknown'),
                'estimated_amount': classification.get('estimated_amount', 0),
                'customer': claim_data.get('user_name', 'Unknown')
            },
            'ai_assessment': {
                'overall_confidence': overall.get('overall_confidence', 0.0),
                'fraud_risk_score': fraud_analysis.get('fraud_risk_score', 0.0),
                'complexity_score': overall.get('complexity_score', 0.0),
                'processing_priority': overall.get('processing_priority', 3),
                'human_review_required': overall.get('human_review_required', True)
            },
            'key_findings': {
                'extracted_entities': len(extraction.get('entities', [])),
                'key_facts': classification.get('key_facts', []),
                'risk_factors': classification.get('risk_factors', []),
                'fraud_indicators': fraud_analysis.get('fraud_indicators', [])
            },
            'recommendations': {
                'next_steps': overall.get('next_steps', []),
                'required_actions': classification.get('recommendations', []),
                'verification_needed': fraud_analysis.get('verification_steps', [])
            },
            'analysis_metadata': {
                'analysis_timestamp': datetime.now().isoformat(),
                'model_version': self.settings.ai.openai_model,
                'processing_time': overall.get('processing_time', 'Unknown'),
                'documents_analyzed': len(analysis_results.get('documents', []))
            }
        }

    async def _share_results_with_agents(self, claim_id: str, analysis_results: Dict):
        """Share analysis results with agents via email and notifications"""
        try:
            # Import here to avoid circular imports
            from .notification_service import NotificationService

            notifications = NotificationService(self.settings)
            claim_data = await self.supabase.get_claim(claim_id)

            if not claim_data:
                return

            # Prepare context for notifications
            summary = analysis_results.get('summary', {})
            guidance = analysis_results.get('agent_guidance', {})

            context = {
                'claim_number': claim_data.get('claim_number'),
                'claim_type': summary.get('claim_overview', {}).get('claim_type', 'Unknown'),
                'ai_confidence': summary.get('ai_assessment', {}).get('overall_confidence', 0.0) * 100,
                'fraud_risk_score': summary.get('ai_assessment', {}).get('fraud_risk_score', 0.0) * 100,
                'estimated_amount': summary.get('claim_overview', {}).get('estimated_amount', 0),
                'recommended_action': guidance.get('approval_guidance', {}).get('recommended_action', 'review'),
                'key_findings': summary.get('key_findings', {}),
                'next_steps': guidance.get('next_actions', []),
                'analysis_timestamp': summary.get('analysis_metadata', {}).get('analysis_timestamp'),
                'user_name': claim_data.get('user_name'),
                'user_email': claim_data.get('user_email')
            }

            # Send notification about completed analysis
            await notifications.send_multi_channel_notification(
                user_email=claim_data.get('user_email'),
                user_phone=None,
                message_type="document_analysis",
                context=context,
                channels=['email', 'slack']
            )

            logger.info(f"Shared analysis results for claim {claim_id}")

        except Exception as e:
            logger.error(f"Error sharing analysis results: {e}")
            
        except Exception as e:
            logger.error(f"Error updating claim scores: {e}")
