"""
ARIA Slack Integration Service via HumanLayer
Human-in-the-loop Slack notifications and team collaboration
"""

import asyncio
import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any

from humanlayer import HumanLayer

from ..config import Settings
from ..database.supabase_client import SupabaseClient
from ..services.zendesk_service import ZendeskService

logger = logging.getLogger(__name__)


class SlackService:
    """Slack integration via HumanLayer for claim processing and team collaboration"""

    def __init__(self, settings: Settings):
        self.settings = settings
        self.supabase = SupabaseClient(settings)
        self.zendesk = ZendeskService(settings)

        # Initialize HumanLayer
        self.humanlayer = HumanLayer(
            api_key=settings.ai.humanlayer_api_key,
            run_id=settings.ai.humanlayer_run_id
        )

        # Slack configuration
        self.claims_channel = settings.slack.claims_channel
        self.alerts_channel = settings.slack.alerts_channel
    
    def _setup_handlers(self):
        """Setup Slack event handlers"""
        
        @self.app.event("app_mention")
        async def handle_app_mention(event, say):
            """Handle app mentions"""
            await say(f"Hello <@{event['user']}>! I'm the ARIA Claims Bot. How can I help you?")
        
        @self.app.command("/claim")
        async def handle_claim_command(ack, respond, command):
            """Handle /claim slash command"""
            await ack()
            
            text = command.get('text', '').strip()
            if not text:
                await respond("Usage: `/claim <claim_number>` to get claim details")
                return
            
            claim_details = await self.get_claim_details(text)
            if claim_details:
                await respond(self._format_claim_details(claim_details))
            else:
                await respond(f"Claim `{text}` not found.")
        
        @self.app.action("assign_claim")
        async def handle_assign_claim(ack, body, respond):
            """Handle claim assignment button"""
            await ack()
            
            user_id = body["user"]["id"]
            claim_id = body["actions"][0]["value"]
            
            # Get user email from Slack
            user_email = await self._get_user_email(user_id)
            if user_email:
                success = await self._assign_claim_to_agent(claim_id, user_email, user_id)
                if success:
                    await respond(f"✅ Claim assigned to <@{user_id}>")
                else:
                    await respond("❌ Failed to assign claim. Please try again.")
            else:
                await respond("❌ Could not find your email address. Please contact admin.")
        
        @self.app.action("escalate_claim")
        async def handle_escalate_claim(ack, body, respond):
            """Handle claim escalation button"""
            await ack()
            
            claim_id = body["actions"][0]["value"]
            user_id = body["user"]["id"]
            
            success = await self._escalate_claim(claim_id, user_id)
            if success:
                await respond("🚨 Claim escalated to senior team")
            else:
                await respond("❌ Failed to escalate claim")
        
        @self.app.action("view_claim")
        async def handle_view_claim(ack, body, respond):
            """Handle view claim button"""
            await ack()
            
            claim_id = body["actions"][0]["value"]
            claim_details = await self.get_claim_details(claim_id)
            
            if claim_details:
                # Create modal with claim details
                modal = self._create_claim_modal(claim_details)
                await self.app.client.views_open(
                    trigger_id=body["trigger_id"],
                    view=modal
                )
            else:
                await respond("❌ Could not load claim details")
    
    async def start_bot(self):
        """Start the Slack bot"""
        try:
            handler = AsyncSocketModeHandler(self.app, self.settings.slack.app_token)
            await handler.start_async()
            logger.info("Slack bot started successfully")
        except Exception as e:
            logger.error(f"Failed to start Slack bot: {e}")
    
    async def send_new_claim_notification(self, claim_data: Dict) -> bool:
        """Send new claim notification to Slack channel via HumanLayer"""
        try:
            # Prepare Slack notification data
            slack_data = {
                "action": "send_slack_notification",
                "channel": self.claims_channel,
                "message": f"🚨 New claim received: {claim_data['claim_number']}",
                "claim_data": claim_data,
                "blocks": self._create_new_claim_blocks(claim_data),
                "requires_assignment": True
            }

            # Get available agents for assignment
            available_agents = await self._get_available_agents()

            # Send enhanced notification via HumanLayer for team assignment
            slack_message = f"""
            🚨 **NEW CLAIM ASSIGNMENT NEEDED** 🚨

            Send Slack notification to channel {self.claims_channel} for new claim requiring team assignment:

            **Claim Details:**
            • Claim Number: {claim_data['claim_number']}
            • Customer: {claim_data.get('user_name', 'N/A')} ({claim_data.get('user_email', 'N/A')})
            • Subject: {claim_data.get('subject', 'N/A')}
            • Priority: {claim_data.get('priority', 'Normal')}
            • Estimated Amount: ${claim_data.get('estimated_amount', 'TBD')}
            • Attachments: {claim_data.get('attachments_count', 0)} files
            • AI Confidence: {claim_data.get('ai_confidence', 'N/A')}%

            **Available Agents for Assignment:**
            {self._format_available_agents(available_agents)}

            **Required Actions:**
            1. **ASSIGN AGENT**: Choose an available team member to handle this claim
            2. **SET PRIORITY**: Confirm or adjust priority level based on claim details
            3. **ADD TO ZENDESK**: Agent will be automatically added as CC to email thread
            4. **NOTIFY CUSTOMER**: Customer will be informed of assignment and agent contact

            **Assignment Options:**
            • React with 🙋‍♂️ to self-assign
            • Tag a team member to assign to them
            • Use assignment buttons in the message

            **SLA Reminder:**
            • Assignment required within: 15 minutes
            • Initial review required within: 2 hours
            • Final decision required within: 4 hours

            Please assign this claim immediately to ensure SLA compliance.
            """

            human_tool = self.humanlayer.human_as_tool()
            result = human_tool(slack_message)

            # Log the notification
            await self._log_slack_notification(claim_data['id'], 'new_claim', 'sent')

            logger.info(f"Sent new claim notification via HumanLayer for {claim_data['claim_number']}")
            return True

        except Exception as e:
            logger.error(f"Error sending Slack notification via HumanLayer: {e}")
            return False
    
    async def send_claim_update(self, claim_data: Dict, update_message: str) -> bool:
        """Send claim status update to Slack via HumanLayer"""
        try:
            slack_data = {
                "action": "send_slack_update",
                "channel": self.claims_channel,
                "message": f"📋 Claim update: {claim_data['claim_number']}",
                "update_message": update_message,
                "claim_data": claim_data,
                "blocks": self._create_update_blocks(claim_data, update_message)
            }

            result = await self.humanlayer.human_as_tool(
                tool_name="slack_claim_update",
                tool_input=slack_data,
                msg=f"Send Slack update for claim {claim_data['claim_number']}: {update_message}"
            )

            return True

        except Exception as e:
            logger.error(f"Error sending claim update via HumanLayer: {e}")
            return False

    async def send_urgent_alert(self, claim_data: Dict, alert_message: str) -> bool:
        """Send urgent claim alert via HumanLayer"""
        try:
            slack_data = {
                "action": "send_urgent_alert",
                "channel": self.alerts_channel,
                "message": f"🚨 URGENT: {claim_data['claim_number']} - {alert_message}",
                "alert_message": alert_message,
                "claim_data": claim_data,
                "blocks": self._create_alert_blocks(claim_data, alert_message),
                "requires_immediate_attention": True
            }

            result = await self.humanlayer.human_as_tool(
                tool_name="slack_urgent_alert",
                tool_input=slack_data,
                msg=f"URGENT: Send immediate Slack alert for claim {claim_data['claim_number']} - {alert_message}"
            )

            return True

        except Exception as e:
            logger.error(f"Error sending urgent alert via HumanLayer: {e}")
            return False
    
    def _create_new_claim_blocks(self, claim_data: Dict) -> List[Dict]:
        """Create Slack blocks for new claim notification"""
        return [
            {
                "type": "header",
                "text": {
                    "type": "plain_text",
                    "text": f"🚨 New Claim: {claim_data['claim_number']}"
                }
            },
            {
                "type": "section",
                "fields": [
                    {
                        "type": "mrkdwn",
                        "text": f"*Customer:* {claim_data.get('user_name', 'N/A')}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": f"*Email:* {claim_data['user_email']}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": f"*Priority:* {self._get_priority_emoji(claim_data.get('priority', 3))}"
                    },
                    {
                        "type": "mrkdwn",
                        "text": f"*Amount:* ${claim_data.get('claimed_amount', 'TBD')}"
                    }
                ]
            },
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": f"*Subject:* {claim_data['subject']}\n*Description:* {claim_data['description'][:200]}..."
                }
            },
            {
                "type": "actions",
                "elements": [
                    {
                        "type": "button",
                        "text": {
                            "type": "plain_text",
                            "text": "🙋‍♂️ Assign to Me"
                        },
                        "style": "primary",
                        "action_id": "assign_claim",
                        "value": claim_data['id']
                    },
                    {
                        "type": "button",
                        "text": {
                            "type": "plain_text",
                            "text": "👁️ View Details"
                        },
                        "action_id": "view_claim",
                        "value": claim_data['id']
                    },
                    {
                        "type": "button",
                        "text": {
                            "type": "plain_text",
                            "text": "🚨 Escalate"
                        },
                        "style": "danger",
                        "action_id": "escalate_claim",
                        "value": claim_data['id']
                    }
                ]
            },
            {
                "type": "context",
                "elements": [
                    {
                        "type": "mrkdwn",
                        "text": f"Zendesk: <{claim_data.get('zendesk_url', '#')}|Ticket #{claim_data.get('zendesk_ticket_id', 'N/A')}> | AI Confidence: {claim_data.get('ai_confidence_score', 0)*100:.1f}%"
                    }
                ]
            }
        ]
    
    def _create_update_blocks(self, claim_data: Dict, update_message: str) -> List[Dict]:
        """Create Slack blocks for claim updates"""
        return [
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": f"📋 *{claim_data['claim_number']}* - {update_message}"
                }
            },
            {
                "type": "context",
                "elements": [
                    {
                        "type": "mrkdwn",
                        "text": f"Status: {claim_data['status']} | Assigned: {claim_data.get('assigned_agent_name', 'Unassigned')}"
                    }
                ]
            }
        ]
    
    def _create_alert_blocks(self, claim_data: Dict, alert_message: str) -> List[Dict]:
        """Create Slack blocks for urgent alerts"""
        return [
            {
                "type": "header",
                "text": {
                    "type": "plain_text",
                    "text": f"🚨 URGENT ALERT"
                }
            },
            {
                "type": "section",
                "text": {
                    "type": "mrkdwn",
                    "text": f"*Claim:* {claim_data['claim_number']}\n*Alert:* {alert_message}"
                }
            },
            {
                "type": "actions",
                "elements": [
                    {
                        "type": "button",
                        "text": {
                            "type": "plain_text",
                            "text": "🔍 Investigate"
                        },
                        "style": "danger",
                        "action_id": "view_claim",
                        "value": claim_data['id']
                    }
                ]
            }
        ]
    
    def _create_claim_modal(self, claim_data: Dict) -> Dict:
        """Create modal view for claim details"""
        return {
            "type": "modal",
            "title": {
                "type": "plain_text",
                "text": f"Claim {claim_data['claim_number']}"
            },
            "blocks": [
                {
                    "type": "section",
                    "fields": [
                        {
                            "type": "mrkdwn",
                            "text": f"*Status:* {claim_data['status']}"
                        },
                        {
                            "type": "mrkdwn",
                            "text": f"*Priority:* {self._get_priority_emoji(claim_data.get('priority', 3))}"
                        },
                        {
                            "type": "mrkdwn",
                            "text": f"*Customer:* {claim_data.get('user_name', 'N/A')}"
                        },
                        {
                            "type": "mrkdwn",
                            "text": f"*Email:* {claim_data['user_email']}"
                        },
                        {
                            "type": "mrkdwn",
                            "text": f"*Amount:* ${claim_data.get('claimed_amount', 'TBD')}"
                        },
                        {
                            "type": "mrkdwn",
                            "text": f"*AI Confidence:* {claim_data.get('ai_confidence_score', 0)*100:.1f}%"
                        }
                    ]
                },
                {
                    "type": "section",
                    "text": {
                        "type": "mrkdwn",
                        "text": f"*Description:*\n{claim_data['description']}"
                    }
                }
            ]
        }
    
    def _get_priority_emoji(self, priority: int) -> str:
        """Get emoji for priority level"""
        priority_map = {
            1: "🔴 Urgent",
            2: "🟡 High", 
            3: "🟢 Normal",
            4: "⚪ Low"
        }
        return priority_map.get(priority, "🟢 Normal")
    
    async def _get_user_email(self, user_id: str) -> Optional[str]:
        """Get user email from Slack user ID"""
        try:
            response = await self.app.client.users_info(user=user_id)
            if response["ok"]:
                return response["user"]["profile"].get("email")
            return None
        except Exception as e:
            logger.error(f"Error getting user email: {e}")
            return None
    
    async def _assign_claim_to_agent(self, claim_id: str, agent_email: str, slack_user_id: str) -> bool:
        """Assign claim to agent"""
        try:
            # Update claim assignment in database
            success = await self.supabase.assign_claim(claim_id, agent_email)
            
            if success:
                # Add agent to Zendesk ticket
                claim_data = await self.supabase.get_claim(claim_id)
                if claim_data and claim_data.get('zendesk_ticket_id'):
                    await self.zendesk.assign_ticket(
                        claim_data['zendesk_ticket_id'],
                        agent_email,
                        f"Claim assigned via Slack by <@{slack_user_id}>"
                    )
                
                logger.info(f"Assigned claim {claim_id} to {agent_email}")
                return True
            
            return False
        
        except Exception as e:
            logger.error(f"Error assigning claim: {e}")
            return False

    async def _get_available_agents(self) -> List[Dict]:
        """Get list of available agents for claim assignment"""
        try:
            # Get all active agents from database
            agents = await self.supabase.get_active_agents()

            available_agents = []
            for agent in agents:
                # Check agent workload
                workload = await self.supabase.get_agent_workload(agent['email'])

                if workload.get('is_available', False):
                    available_agents.append({
                        'id': agent['id'],
                        'name': agent.get('name', agent['email']),
                        'email': agent['email'],
                        'specializations': agent.get('specializations', []),
                        'current_workload': workload.get('active_claims_count', 0),
                        'max_workload': workload.get('max_workload', 10),
                        'last_active': agent.get('last_active_at'),
                        'slack_user_id': agent.get('slack_user_id')
                    })

            return available_agents

        except Exception as e:
            logger.error(f"Error getting available agents: {e}")
            return []

    def _format_available_agents(self, agents: List[Dict]) -> str:
        """Format available agents list for Slack message"""
        if not agents:
            return "⚠️ No agents currently available - escalation may be required"

        formatted = []
        for agent in agents:
            specializations = ", ".join(agent.get('specializations', ['General']))
            workload = f"{agent.get('current_workload', 0)}/{agent.get('max_workload', 10)}"

            formatted.append(
                f"• **{agent['name']}** ({agent['email']})\n"
                f"  - Specializations: {specializations}\n"
                f"  - Current workload: {workload} claims\n"
                f"  - Status: {'🟢 Available' if agent.get('current_workload', 0) < agent.get('max_workload', 10) else '🟡 Near capacity'}"
            )

        return "\n".join(formatted)

    async def _log_slack_notification(self, claim_id: str, notification_type: str, status: str, error: str = None):
        """Log Slack notification to database"""
        try:
            log_data = {
                'claim_id': claim_id,
                'channel': self.claims_channel,
                'notification_type': notification_type,
                'status': status,
                'sent_at': datetime.now().isoformat(),
                'error_message': error
            }

            await self.supabase.log_slack_notification(log_data)

        except Exception as e:
            logger.error(f"Error logging Slack notification: {e}")

    async def send_agent_assignment_notification(self, claim_data: Dict, agent_email: str) -> bool:
        """Send notification when agent is assigned to claim"""
        try:
            slack_message = f"""
            👥 **CLAIM ASSIGNED** 👥

            Send Slack notification to channel {self.claims_channel} about claim assignment:

            **Assignment Details:**
            • Claim Number: {claim_data['claim_number']}
            • Assigned Agent: {agent_email}
            • Customer: {claim_data.get('user_name', 'N/A')} ({claim_data.get('user_email', 'N/A')})
            • Priority: {claim_data.get('priority', 'Normal')}
            • Assignment Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S UTC')}

            **Next Steps Completed:**
            ✅ Agent assigned to claim
            ✅ Agent added as CC to Zendesk email thread
            ✅ Customer notification sent
            ✅ SLA timer started

            **Agent Responsibilities:**
            • Review claim details and documentation
            • Contact customer if additional information needed
            • Make approval/denial decision within SLA
            • Update Zendesk ticket with progress

            **SLA Reminders:**
            • Initial review: Within 2 hours
            • Final decision: Within 4 hours
            • Customer updates: As needed

            The claim is now in active review.
            """

            human_tool = self.humanlayer.human_as_tool()
            result = human_tool(slack_message)

            await self._log_slack_notification(claim_data['id'], 'agent_assignment', 'sent')

            logger.info(f"Sent agent assignment notification for claim {claim_data['claim_number']}")
            return True

        except Exception as e:
            logger.error(f"Error sending agent assignment notification: {e}")
            await self._log_slack_notification(claim_data.get('id'), 'agent_assignment', 'failed', str(e))
            return False
    
    async def _escalate_claim(self, claim_id: str, escalated_by: str) -> bool:
        """Escalate claim to senior team"""
        try:
            # Update claim status
            success = await self.supabase.escalate_claim(claim_id, escalated_by)
            
            if success:
                # Send alert to alerts channel
                claim_data = await self.supabase.get_claim(claim_id)
                await self.send_urgent_alert(
                    claim_data,
                    f"Escalated by <@{escalated_by}> - Requires senior review"
                )
                
                logger.info(f"Escalated claim {claim_id}")
                return True
            
            return False
        
        except Exception as e:
            logger.error(f"Error escalating claim: {e}")
            return False
    
    async def get_claim_details(self, claim_identifier: str) -> Optional[Dict]:
        """Get claim details by claim number or ID"""
        try:
            return await self.supabase.get_claim_by_number(claim_identifier)
        except Exception as e:
            logger.error(f"Error getting claim details: {e}")
            return None
    
    def _format_claim_details(self, claim_data: Dict) -> str:
        """Format claim details for Slack message"""
        return f"""
📋 *Claim Details: {claim_data['claim_number']}*

*Customer:* {claim_data.get('user_name', 'N/A')} ({claim_data['user_email']})
*Status:* {claim_data['status']}
*Priority:* {self._get_priority_emoji(claim_data.get('priority', 3))}
*Amount:* ${claim_data.get('claimed_amount', 'TBD')}
*AI Confidence:* {claim_data.get('ai_confidence_score', 0)*100:.1f}%
*Assigned:* {claim_data.get('assigned_agent_name', 'Unassigned')}

*Description:* {claim_data['description'][:300]}...

*Zendesk:* <{claim_data.get('zendesk_url', '#')}|Ticket #{claim_data.get('zendesk_ticket_id', 'N/A')}>
        """.strip()
