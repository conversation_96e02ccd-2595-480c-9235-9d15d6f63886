"""
ARIA Multi-Channel Notification Service
Unified notification system using HumanLayer for Email, SMS, Slack
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from pathlib import Path
from jinja2 import Environment, FileSystemLoader

import aiohttp
from humanlayer import HumanLayer

from ..config import Settings
from ..database.supabase_client import SupabaseClient

logger = logging.getLogger(__name__)


class NotificationService:
    """Multi-channel notification service using HumanLayer"""

    def __init__(self, settings: Settings):
        self.settings = settings
        self.supabase = SupabaseClient(settings)

        # Initialize HumanLayer
        self.humanlayer = HumanLayer(
            api_key=settings.ai.humanlayer_api_key,
            run_id=settings.ai.humanlayer_run_id
        )

        # Initialize template engine
        self._init_templates()

        # Initialize notification channels
        self._init_humanlayer_channels()
    
    def _init_humanlayer_channels(self):
        """Initialize HumanLayer notification channels based on configuration"""
        try:
            # Check which channels are enabled in configuration
            self.email_enabled = getattr(self.settings.notifications, 'enable_email_notifications', True)
            self.sms_enabled = getattr(self.settings.notifications, 'enable_sms_notifications', False)
            self.slack_enabled = getattr(self.settings.notifications, 'enable_slack_notifications', True)
            self.whatsapp_enabled = getattr(self.settings.notifications, 'enable_whatsapp_notifications', False)

            enabled_channels = []
            if self.email_enabled: enabled_channels.append("Email")
            if self.slack_enabled: enabled_channels.append("Slack")
            if self.sms_enabled: enabled_channels.append("SMS")
            if self.whatsapp_enabled: enabled_channels.append("WhatsApp")

            logger.info(f"HumanLayer notification channels initialized: {', '.join(enabled_channels)}")

        except Exception as e:
            logger.warning(f"HumanLayer service not available: {e}")
            self.email_enabled = False
            self.sms_enabled = False
            self.slack_enabled = False
            self.whatsapp_enabled = False
    
    def _init_templates(self):
        """Initialize Jinja2 template engine"""
        template_dir = Path(__file__).parent.parent / "templates" / "notifications"
        template_dir.mkdir(parents=True, exist_ok=True)
        
        self.template_env = Environment(
            loader=FileSystemLoader(str(template_dir)),
            autoescape=True
        )
        
        # Create default templates if they don't exist
        self._create_default_templates(template_dir)
    
    def _create_default_templates(self, template_dir: Path):
        """Create default notification templates"""
        templates = {
            "claim_received_email.html": """
            <h2>Claim Received - {{ claim_number }}</h2>
            <p>Dear {{ user_name or 'Valued Customer' }},</p>
            <p>We have received your insurance claim and are processing it immediately.</p>
            <p><strong>Claim Reference:</strong> {{ claim_number }}</p>
            <p><strong>Status:</strong> Documents received and under verification</p>
            <p><a href="{{ tracking_link }}" style="background: #007bff; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Track Your Claim</a></p>
            <p>You will receive updates at each step of the process.</p>
            <p>Best regards,<br>ARIA Claims Team</p>
            """,
            
            "claim_received_sms.txt": """
            ARIA Claims: Your claim {{ claim_number }} has been received. Track progress: {{ tracking_link }}
            """,
            
            "claim_received_whatsapp.txt": """
            ✅ Your insurance claim has been received!
            
            📋 Reference: {{ claim_number }}
            🔍 Status: Under verification
            📱 Track: {{ tracking_link }}
            
            You'll get updates at each step.
            """,
            
            "agent_assignment_email.html": """
            <h2>New Claim Assignment - {{ claim_number }}</h2>
            <p>Hello {{ agent_name }},</p>
            <p>A new claim has been assigned to you for review.</p>
            <p><strong>Claim:</strong> {{ claim_number }}</p>
            <p><strong>Customer:</strong> {{ customer_name }} ({{ customer_email }})</p>
            <p><strong>Priority:</strong> {{ priority }}</p>
            <p><strong>AI Confidence:</strong> {{ ai_confidence }}%</p>
            <p><a href="{{ review_link }}" style="background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;">Review Claim</a></p>
            <p>Zendesk Ticket: <a href="{{ zendesk_url }}">{{ zendesk_ticket_id }}</a></p>
            """,
            
            "status_update_email.html": """
            <h2>Claim Update - {{ claim_number }}</h2>
            <p>Dear {{ user_name or 'Valued Customer' }},</p>
            <p>Your claim status has been updated:</p>
            <p><strong>New Status:</strong> {{ new_status }}</p>
            <p><strong>Update:</strong> {{ update_message }}</p>
            {% if eta %}
            <p><strong>Expected completion:</strong> {{ eta }}</p>
            {% endif %}
            <p><a href="{{ tracking_link }}">View Full Details</a></p>
            """,
            
            "approval_decision_email.html": """
            <h2>Claim Decision - {{ claim_number }}</h2>
            <p>Dear {{ user_name or 'Valued Customer' }},</p>
            {% if decision == 'approved' %}
            <p style="color: green;"><strong>✅ Your claim has been APPROVED</strong></p>
            <p><strong>Approved Amount:</strong> ${{ approved_amount }}</p>
            <p>Payment will be processed within 3-5 business days.</p>
            {% elif decision == 'denied' %}
            <p style="color: red;"><strong>❌ Your claim has been DENIED</strong></p>
            <p><strong>Reason:</strong> {{ denial_reason }}</p>
            <p>You have the right to appeal this decision.</p>
            {% endif %}
            <p><a href="{{ tracking_link }}">View Complete Details</a></p>
            """
        }
        
        for filename, content in templates.items():
            template_file = template_dir / filename
            if not template_file.exists():
                template_file.write_text(content.strip())
    
    async def send_multi_channel_notification(
        self,
        user_email: str,
        user_phone: Optional[str],
        message_type: str,
        context: Dict[str, Any],
        channels: List[str] = None
    ):
        """Send notification across enabled channels using HumanLayer"""
        # Determine which channels to use based on configuration
        if channels is None:
            channels = []
            if self.email_enabled:
                channels.append('email')
            if self.slack_enabled:
                channels.append('slack')
            # SMS and WhatsApp are disabled

        # Filter out disabled channels
        enabled_channels = []
        for channel in channels:
            if channel == 'email' and self.email_enabled:
                enabled_channels.append(channel)
            elif channel == 'slack' and self.slack_enabled:
                enabled_channels.append(channel)
            elif channel == 'sms' and self.sms_enabled and user_phone:
                enabled_channels.append(channel)
            elif channel == 'whatsapp' and self.whatsapp_enabled and user_phone:
                enabled_channels.append(channel)

        results = {}

        try:
            # Prepare notification content
            notification_content = await self._prepare_notification_content(message_type, context)

            # Send via enabled channels only
            if 'email' in enabled_channels:
                email_result = await self._send_humanlayer_email(
                    user_email, notification_content, context
                )
                results['email'] = email_result

            if 'slack' in enabled_channels:
                slack_result = await self._send_humanlayer_slack(
                    notification_content, context
                )
                results['slack'] = slack_result

            # SMS and WhatsApp are disabled - skip these channels
            if 'sms' in channels and not self.sms_enabled:
                results['sms'] = 'disabled'
                logger.info("SMS notifications are disabled")

            if 'whatsapp' in channels and not self.whatsapp_enabled:
                results['whatsapp'] = 'disabled'
                logger.info("WhatsApp notifications are disabled")

            # Log notification results
            await self._log_notifications(user_email, message_type, results, context)

            return results

        except Exception as e:
            logger.error(f"Multi-channel notification failed: {e}")
            return {'error': str(e)}
    
    async def _send_humanlayer_email(
        self,
        to_email: str,
        content: Dict[str, str],
        context: Dict[str, Any]
    ) -> bool:
        """Send email notification via HumanLayer"""
        try:
            # Use HumanLayer's human-as-tool for email sending
            email_message = f"""
            Send email notification to {to_email} regarding claim {context.get('claim_number', 'N/A')}

            Subject: {content.get('subject', 'Claim Notification')}

            Email Body:
            {content.get('email_body', 'Claim notification content')}

            Context: {context}
            """

            # Send via HumanLayer
            human_tool = self.humanlayer.human_as_tool()
            result = human_tool(email_message)

            logger.info(f"Email notification sent via HumanLayer to {to_email}")
            return True

        except Exception as e:
            logger.error(f"Error sending email via HumanLayer to {to_email}: {e}")
            return False

    # SMS functionality removed per requirements - only email and Slack supported

    async def _send_humanlayer_slack(
        self,
        content: Dict[str, str],
        context: Dict[str, Any]
    ) -> bool:
        """Send Slack notification via HumanLayer"""
        try:
            slack_message = f"""
            Send Slack notification regarding claim {context.get('claim_number', 'N/A')}

            Channel: {context.get('slack_channel', '#claims-processing')}

            Message:
            {content.get('slack_body', 'Claim notification')}

            Attachments: {content.get('slack_attachments', [])}

            Context: {context}
            """

            human_tool = self.humanlayer.human_as_tool()
            result = human_tool(slack_message)

            logger.info(f"Slack notification sent via HumanLayer")
            return True

        except Exception as e:
            logger.error(f"Error sending Slack notification via HumanLayer: {e}")
            return False
    
    async def _prepare_notification_content(
        self,
        message_type: str,
        context: Dict[str, Any]
    ) -> Dict[str, str]:
        """Prepare notification content for all channels"""
        try:
            # Enhance context with tracking links and ETAs
            enhanced_context = await self._enhance_context(context)

            content = {}

            # Generate email content
            if self._template_exists(f"{message_type}_email.html"):
                email_template = self.template_env.get_template(f"{message_type}_email.html")
                content['email_body'] = email_template.render(enhanced_context)
                content['subject'] = self._generate_email_subject(message_type, enhanced_context)
            else:
                # Fallback email content
                content['email_body'] = self._generate_fallback_email(message_type, enhanced_context)
                content['subject'] = self._generate_email_subject(message_type, enhanced_context)

            # Generate Slack content
            content['slack_body'] = self._generate_slack_message(message_type, enhanced_context)
            content['slack_attachments'] = self._generate_slack_attachments(message_type, enhanced_context)

            return content

        except Exception as e:
            logger.error(f"Error preparing notification content: {e}")
            return {
                'email_body': f"Notification: {message_type}",
                'slack_body': f"Claim update: {context.get('claim_number', 'N/A')}",
                'subject': f"Claim Notification - {context.get('claim_number', 'N/A')}"
            }

    async def _enhance_context(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Enhance context with tracking links, ETAs, and additional information"""
        enhanced = context.copy()

        # Add tracking link if claim number exists
        if 'claim_number' in context:
            enhanced['tracking_link'] = self._generate_tracking_link(context['claim_number'])
            enhanced['agent_review_link'] = self._generate_agent_review_link(context['claim_number'])

        # Add ETA if status exists
        if 'status' in context or 'new_status' in context:
            current_status = context.get('new_status', context.get('status', ''))
            enhanced['eta'] = self._calculate_eta(current_status)

        # Add timestamp
        enhanced['notification_timestamp'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S UTC")

        # Add support contact information
        enhanced['support_email'] = self.settings.email.reply_to
        enhanced['support_phone'] = getattr(self.settings, 'support_phone', '1-800-CLAIMS')

        return enhanced

    def _generate_fallback_email(self, message_type: str, context: Dict[str, Any]) -> str:
        """Generate fallback email content when template is not found"""
        claim_number = context.get('claim_number', 'N/A')
        user_name = context.get('user_name', context.get('sender_name', 'Valued Customer'))
        tracking_link = context.get('tracking_link', '#')

        return f"""
        <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                <h2 style="color: #0066cc;">ARIA Claims Processing Update</h2>
                <p>Dear {user_name},</p>
                <p>This is an update regarding your insurance claim: <strong>{claim_number}</strong></p>
                <p><strong>Notification Type:</strong> {message_type.replace('_', ' ').title()}</p>
                <p><strong>Status:</strong> {context.get('new_status', context.get('status', 'Processing'))}</p>
                {f"<p><strong>Message:</strong> {context.get('update_message', context.get('message', ''))}</p>" if context.get('update_message') or context.get('message') else ""}
                {f"<p><strong>ETA:</strong> {context.get('eta', '')}</p>" if context.get('eta') else ""}
                <div style="margin: 30px 0;">
                    <a href="{tracking_link}" style="background-color: #0066cc; color: white; padding: 12px 24px; text-decoration: none; border-radius: 5px; display: inline-block;">Track Your Claim</a>
                </div>
                <p>If you have any questions, please contact us at {context.get('support_email', '<EMAIL>')}</p>
                <p>Best regards,<br><strong>ARIA Claims Processing Team</strong></p>
                <hr style="margin-top: 30px; border: none; border-top: 1px solid #eee;">
                <p style="font-size: 12px; color: #666;">
                    This is an automated notification from the ARIA Claims Processing System.<br>
                    Sent at: {context.get('notification_timestamp', 'N/A')}
                </p>
            </div>
        </body>
        </html>
        """
    
    def _template_exists(self, template_name: str) -> bool:
        """Check if template exists"""
        try:
            self.template_env.get_template(template_name)
            return True
        except:
            return False

    def _generate_slack_message(self, message_type: str, context: Dict[str, Any]) -> str:
        """Generate Slack message content"""
        claim_number = context.get('claim_number', 'N/A')
        user_name = context.get('user_name', context.get('customer_name', 'N/A'))

        if message_type == 'claim_received':
            return f"""🚨 *New Claim Received*
*Claim:* {claim_number}
*Customer:* {user_name} ({context.get('user_email', context.get('customer_email', 'N/A'))})
*Status:* Processing started
*Attachments:* {context.get('attachments_count', 0)} files
*Priority:* {context.get('priority', 'Normal')}
*ETA:* {context.get('eta', '2-4 hours')}

<{context.get('tracking_link', '#')}|Track Claim> | <{context.get('agent_review_link', '#')}|Assign Agent>"""

        elif message_type == 'status_update':
            status_emoji = {
                'received': '📥',
                'documents_processing': '📄',
                'ai_analysis': '🤖',
                'human_review': '👥',
                'approved': '✅',
                'denied': '❌',
                'more_info_required': '📝',
                'escalated': '🚨'
            }.get(context.get('new_status', ''), '📋')

            return f"""{status_emoji} *Claim Status Update*
*Claim:* {claim_number}
*Customer:* {user_name}
*Previous Status:* {context.get('status', 'Processing')}
*New Status:* {context.get('new_status', 'N/A')}
*Update:* {context.get('update_message', 'N/A')}
*ETA:* {context.get('eta', 'Processing in progress')}

<{context.get('tracking_link', '#')}|View Details> | <{context.get('zendesk_url', '#')}|Zendesk Ticket>"""

        elif message_type == 'agent_assignment':
            return f"""👥 *Claim Assignment*
*Claim:* {claim_number}
*Assigned to:* {context.get('agent_name', context.get('agent_email', 'N/A'))}
*Customer:* {user_name}
*Priority:* {context.get('priority', 'Normal')}
*AI Confidence:* {context.get('ai_confidence', 'N/A')}%
*Estimated Amount:* ${context.get('estimated_amount', 'TBD')}

<{context.get('agent_review_link', '#')}|Review Claim> | <{context.get('zendesk_url', '#')}|Zendesk Ticket>"""

        elif message_type == 'escalation':
            return f"""🚨 *URGENT: Claim Escalated*
*Claim:* {claim_number}
*Customer:* {user_name}
*Escalation Reason:* {context.get('escalation_reason', 'Requires senior review')}
*Original Agent:* {context.get('agent_name', 'N/A')}
*Priority:* HIGH

<{context.get('agent_review_link', '#')}|Review Immediately> | <{context.get('zendesk_url', '#')}|Zendesk Ticket>"""

        elif message_type == 'document_analysis':
            return f"""🔍 *Document Analysis Complete*
*Claim:* {claim_number}
*Customer:* {user_name}
*Documents Analyzed:* {context.get('documents_count', 'N/A')} files
*AI Confidence:* {context.get('ai_confidence', 'N/A')}%
*Next Step:* Agent assignment

<{context.get('agent_review_link', '#')}|Assign Agent> | <{context.get('tracking_link', '#')}|View Analysis>"""

        else:
            return f"""📢 *Claim Notification*
*Claim:* {claim_number}
*Type:* {message_type.replace('_', ' ').title()}
*Customer:* {user_name}

<{context.get('tracking_link', '#')}|View Details>"""

    def _generate_slack_attachments(self, message_type: str, context: Dict[str, Any]) -> List[Dict]:
        """Generate Slack message attachments"""
        attachments = []

        if message_type == 'claim_received':
            attachments.append({
                "color": "good",
                "title": "New Claim Details",
                "fields": [
                    {"title": "Claim Number", "value": context.get('claim_number', 'N/A'), "short": True},
                    {"title": "Customer", "value": context.get('user_name', 'N/A'), "short": True},
                    {"title": "Email", "value": context.get('user_email', 'N/A'), "short": True},
                    {"title": "Priority", "value": context.get('priority', 'Normal'), "short": True},
                    {"title": "Attachments", "value": f"{context.get('attachments_count', 0)} files", "short": True},
                    {"title": "ETA", "value": context.get('eta', '2-4 hours'), "short": True}
                ],
                "actions": [
                    {
                        "type": "button",
                        "text": "Assign Agent",
                        "url": context.get('agent_review_link', '#'),
                        "style": "primary"
                    },
                    {
                        "type": "button",
                        "text": "View Details",
                        "url": context.get('tracking_link', '#')
                    }
                ]
            })

        elif message_type == 'agent_assignment':
            color = "warning" if context.get('priority', '').lower() == 'high' else "good"
            attachments.append({
                "color": color,
                "title": "Claim Assignment Details",
                "fields": [
                    {"title": "Assigned Agent", "value": context.get('agent_name', context.get('agent_email', 'N/A')), "short": True},
                    {"title": "AI Confidence", "value": f"{context.get('ai_confidence', 'N/A')}%", "short": True},
                    {"title": "Estimated Amount", "value": f"${context.get('estimated_amount', 'TBD')}", "short": True},
                    {"title": "Claim Type", "value": context.get('claim_type', 'General'), "short": True}
                ],
                "actions": [
                    {
                        "type": "button",
                        "text": "Review Claim",
                        "url": context.get('agent_review_link', '#'),
                        "style": "primary"
                    },
                    {
                        "type": "button",
                        "text": "Zendesk Ticket",
                        "url": context.get('zendesk_url', '#')
                    }
                ]
            })

        elif message_type == 'status_update':
            status_colors = {
                'approved': 'good',
                'denied': 'danger',
                'escalated': 'danger',
                'more_info_required': 'warning'
            }
            color = status_colors.get(context.get('new_status', ''), '#0066cc')

            attachments.append({
                "color": color,
                "title": "Status Update Details",
                "fields": [
                    {"title": "Previous Status", "value": context.get('status', 'Processing'), "short": True},
                    {"title": "New Status", "value": context.get('new_status', 'N/A'), "short": True},
                    {"title": "ETA", "value": context.get('eta', 'Processing'), "short": True},
                    {"title": "Updated", "value": context.get('notification_timestamp', 'Now'), "short": True}
                ],
                "actions": [
                    {
                        "type": "button",
                        "text": "View Details",
                        "url": context.get('tracking_link', '#')
                    }
                ]
            })

        elif message_type == 'escalation':
            attachments.append({
                "color": "danger",
                "title": "🚨 URGENT ESCALATION",
                "fields": [
                    {"title": "Escalation Reason", "value": context.get('escalation_reason', 'Requires review'), "short": False},
                    {"title": "Original Agent", "value": context.get('agent_name', 'N/A'), "short": True},
                    {"title": "Priority", "value": "HIGH", "short": True}
                ],
                "actions": [
                    {
                        "type": "button",
                        "text": "Review Immediately",
                        "url": context.get('agent_review_link', '#'),
                        "style": "danger"
                    }
                ]
            })

        return attachments
    
    async def send_email(
        self,
        to_email: str,
        subject: str,
        template: str,
        context: Dict[str, Any]
    ) -> bool:
        """Send email using template"""
        try:
            # Try to load template, fallback to basic template if not found
            try:
                template_obj = self.template_env.get_template(f"{template}")
                html_content = template_obj.render(context)
            except Exception as template_error:
                logger.warning(f"Template {template} not found, using fallback: {template_error}")
                # Create basic HTML content
                html_content = f"""
                <html>
                <body>
                    <h2>{subject}</h2>
                    <p>Dear {context.get('sender_name', 'Customer')},</p>
                    <p>This is a notification regarding your request.</p>
                    <p>Context: {context}</p>
                    <p>Best regards,<br>ARIA Claims Team</p>
                </body>
                </html>
                """

            # Send via HumanLayer
            email_message = f"""
            Send email to {to_email}

            Subject: {subject}

            HTML Content:
            {html_content}

            Context: {context}
            """

            human_tool = self.humanlayer.human_as_tool()
            result = human_tool(email_message)

            logger.info(f"Email sent via HumanLayer to {to_email}")
            return True

        except Exception as e:
            logger.error(f"Error sending email to {to_email}: {e}")
            return False

    async def send_slack_notification(
        self,
        channel: str,
        message: str,
        attachments: List[Dict] = None,
        blocks: List[Dict] = None
    ) -> bool:
        """Send Slack notification"""
        try:
            webhook_url = self.settings.slack.webhook_url
            
            payload = {
                "channel": channel,
                "text": message,
                "username": "ARIA Claims Bot",
                "icon_emoji": ":robot_face:"
            }
            
            if attachments:
                payload["attachments"] = attachments
            
            if blocks:
                payload["blocks"] = blocks
            
            async with aiohttp.ClientSession() as session:
                async with session.post(webhook_url, json=payload) as response:
                    if response.status == 200:
                        logger.info(f"Slack notification sent to {channel}")
                        return True
                    else:
                        logger.error(f"Slack notification failed: {response.status}")
                        return False
        
        except Exception as e:
            logger.error(f"Error sending Slack notification: {e}")
            return False
    
    def _generate_email_subject(self, template: str, context: Dict) -> str:
        """Generate email subject based on template and context"""
        subjects = {
            "claim_received": f"✅ Claim Received - {context.get('claim_number', 'New Claim')}",
            "status_update": f"📋 Claim Update - {context.get('claim_number', 'Your Claim')}",
            "agent_assignment": f"👥 New Claim Assignment - {context.get('claim_number', '')}",
            "approval_decision": f"🎯 Claim Decision - {context.get('claim_number', 'Your Claim')}",
            "request_attachments": "📎 Additional Documents Required for Your Claim",
            "claim_guidance": "🛡️ How to Submit Your Insurance Claim",
            "agent_notification": f"🔔 Claim Assignment - {context.get('claim_number', '')}",
            "processing_error": f"⚠️ Processing Issue - {context.get('claim_number', 'Your Request')}",
            "escalation": f"🚨 Claim Escalated - {context.get('claim_number', '')}",
            "document_analysis": f"🔍 Document Analysis Complete - {context.get('claim_number', '')}",
            "more_info_request": f"📝 Additional Information Needed - {context.get('claim_number', '')}"
        }

        template_key = template.replace("_email", "")
        return subjects.get(template_key, "ARIA Claims Notification")

    def _generate_tracking_link(self, claim_number: str) -> str:
        """Generate dynamic tracking link for claim"""
        return f"{self.settings.app.base_url}/track/{claim_number}"

    def _generate_agent_review_link(self, claim_number: str) -> str:
        """Generate agent review link for claim"""
        return f"{self.settings.app.base_url}/agent/review/{claim_number}"

    def _calculate_eta(self, current_status: str) -> str:
        """Calculate estimated completion time based on current status"""
        eta_mapping = {
            'received': '2-4 hours',
            'documents_processing': '1-2 hours',
            'ai_analysis': '30-60 minutes',
            'human_review': '2-4 hours',
            'pending_approval': '1-2 hours',
            'more_info_required': 'Pending your response',
            'escalated': '4-8 hours'
        }
        return eta_mapping.get(current_status, 'Processing in progress')
    
    async def _log_notifications(
        self,
        recipient: str,
        message_type: str,
        results: Dict,
        context: Dict
    ):
        """Log notification results to database"""
        try:
            for channel, success in results.items():
                notification_data = {
                    'recipient': recipient,
                    'channel': channel,
                    'message_type': message_type,
                    'status': 'sent' if success else 'failed',
                    'context': context,
                    'sent_at': datetime.now() if success else None
                }
                
                await self.supabase.log_notification(notification_data)
        
        except Exception as e:
            logger.error(f"Error logging notifications: {e}")
